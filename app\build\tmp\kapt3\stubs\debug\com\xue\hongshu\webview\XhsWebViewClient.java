package com.xue.hongshu.webview;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0003\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\b\u0007\u0018\u0000 62\u00020\u0001:\u000267BC\u0012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003\u0012\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00050\u0003\u0012\u0014\b\u0002\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00050\u0003\u00a2\u0006\u0002\u0010\nJ\u0012\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0015H\u0002J\b\u0010\u0016\u001a\u00020\fH\u0002J\b\u0010\u0017\u001a\u00020\u0005H\u0002J\u0010\u0010\u0018\u001a\u00020\u00052\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J\b\u0010\u001b\u001a\u00020\u0007H\u0002J$\u0010\u001c\u001a\u00020\u00052\u0006\u0010\u001d\u001a\u00020\u00072\u0006\u0010\u001e\u001a\u00020\u001f2\n\b\u0002\u0010 \u001a\u0004\u0018\u00010!H\u0002J\u0010\u0010\"\u001a\u00020\f2\u0006\u0010#\u001a\u00020\u0007H\u0002J\u001c\u0010$\u001a\u00020\u00052\b\u0010%\u001a\u0004\u0018\u00010\u001a2\b\u0010#\u001a\u0004\u0018\u00010\u0007H\u0016J&\u0010&\u001a\u00020\u00052\b\u0010%\u001a\u0004\u0018\u00010\u001a2\b\u0010#\u001a\u0004\u0018\u00010\u00072\b\u0010\'\u001a\u0004\u0018\u00010(H\u0016J&\u0010)\u001a\u00020\u00052\b\u0010%\u001a\u0004\u0018\u00010\u001a2\b\u0010*\u001a\u0004\u0018\u00010+2\b\u0010\u0014\u001a\u0004\u0018\u00010,H\u0016J&\u0010-\u001a\u00020\u00052\b\u0010%\u001a\u0004\u0018\u00010\u001a2\b\u0010.\u001a\u0004\u0018\u00010/2\b\u0010\u0014\u001a\u0004\u0018\u00010\u0015H\u0016J\u0010\u00100\u001a\u00020\u00052\u0006\u00101\u001a\u00020\u0007H\u0002J\u0010\u00102\u001a\u00020\u00042\u0006\u00103\u001a\u00020\u0007H\u0002J\u0012\u00104\u001a\u00020\u00052\b\u0010\u0014\u001a\u0004\u0018\u00010\u0015H\u0002J\b\u00105\u001a\u00020\u0005H\u0002R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082D\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00050\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00050\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00068"}, d2 = {"Lcom/xue/hongshu/webview/XhsWebViewClient;", "Landroid/webkit/WebViewClient;", "onDataExtracted", "Lkotlin/Function1;", "Lcom/xue/hongshu/webview/XhsUserData;", "", "onError", "", "onRetry", "Lcom/xue/hongshu/utils/ErrorHandler$ErrorInfo;", "(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V", "isExtracting", "", "maxRetries", "", "retryCount", "timeoutJob", "Lkotlinx/coroutines/Job;", "analyzeSslError", "Lcom/xue/hongshu/webview/XhsWebViewClient$SslErrorInfo;", "error", "Landroid/net/http/SslError;", "canRetryOnSslError", "cancelTimeoutCheck", "extractUserData", "webView", "Landroid/webkit/WebView;", "getDataExtractionScript", "handleError", "message", "type", "Lcom/xue/hongshu/utils/ErrorHandler$ErrorType;", "cause", "", "isXiaohongshuDomain", "url", "onPageFinished", "view", "onPageStarted", "favicon", "Landroid/graphics/Bitmap;", "onReceivedError", "request", "Landroid/webkit/WebResourceRequest;", "Landroid/webkit/WebResourceError;", "onReceivedSslError", "handler", "Landroid/webkit/SslErrorHandler;", "parseAndHandleResult", "result", "parseUserDataFromJson", "json", "recordSslError", "startTimeoutCheck", "Companion", "SslErrorInfo", "app_debug"})
public final class XhsWebViewClient extends android.webkit.WebViewClient {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.xue.hongshu.webview.XhsUserData, kotlin.Unit> onDataExtracted = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.String, kotlin.Unit> onError = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.xue.hongshu.utils.ErrorHandler.ErrorInfo, kotlin.Unit> onRetry = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "XhsWebViewClient";
    private static final long MIN_DELAY = 2000L;
    private static final long MAX_DELAY = 5000L;
    private static final long TIMEOUT_DELAY = 30000L;
    private boolean isExtracting = false;
    private int retryCount = 0;
    private final int maxRetries = 3;
    @org.jetbrains.annotations.Nullable()
    private kotlinx.coroutines.Job timeoutJob;
    @org.jetbrains.annotations.NotNull()
    public static final com.xue.hongshu.webview.XhsWebViewClient.Companion Companion = null;
    
    public XhsWebViewClient(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.xue.hongshu.webview.XhsUserData, kotlin.Unit> onDataExtracted, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onError, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.xue.hongshu.utils.ErrorHandler.ErrorInfo, kotlin.Unit> onRetry) {
        super();
    }
    
    @java.lang.Override()
    public void onPageFinished(@org.jetbrains.annotations.Nullable()
    android.webkit.WebView view, @org.jetbrains.annotations.Nullable()
    java.lang.String url) {
    }
    
    @java.lang.Override()
    public void onPageStarted(@org.jetbrains.annotations.Nullable()
    android.webkit.WebView view, @org.jetbrains.annotations.Nullable()
    java.lang.String url, @org.jetbrains.annotations.Nullable()
    android.graphics.Bitmap favicon) {
    }
    
    @java.lang.Override()
    public void onReceivedError(@org.jetbrains.annotations.Nullable()
    android.webkit.WebView view, @org.jetbrains.annotations.Nullable()
    android.webkit.WebResourceRequest request, @org.jetbrains.annotations.Nullable()
    android.webkit.WebResourceError error) {
    }
    
    @java.lang.Override()
    public void onReceivedSslError(@org.jetbrains.annotations.Nullable()
    android.webkit.WebView view, @org.jetbrains.annotations.Nullable()
    android.webkit.SslErrorHandler handler, @org.jetbrains.annotations.Nullable()
    android.net.http.SslError error) {
    }
    
    /**
     * 分析SSL错误详情
     */
    private final com.xue.hongshu.webview.XhsWebViewClient.SslErrorInfo analyzeSslError(android.net.http.SslError error) {
        return null;
    }
    
    /**
     * 检查是否为小红书域名
     */
    private final boolean isXiaohongshuDomain(java.lang.String url) {
        return false;
    }
    
    /**
     * 记录SSL错误统计
     */
    private final void recordSslError(android.net.http.SslError error) {
    }
    
    /**
     * 判断是否可以在SSL错误时重试
     */
    private final boolean canRetryOnSslError() {
        return false;
    }
    
    private final void extractUserData(android.webkit.WebView webView) {
    }
    
    private final void startTimeoutCheck() {
    }
    
    private final void cancelTimeoutCheck() {
    }
    
    private final void parseAndHandleResult(java.lang.String result) {
    }
    
    private final void handleError(java.lang.String message, com.xue.hongshu.utils.ErrorHandler.ErrorType type, java.lang.Throwable cause) {
    }
    
    private final com.xue.hongshu.webview.XhsUserData parseUserDataFromJson(java.lang.String json) {
        return null;
    }
    
    private final java.lang.String getDataExtractionScript() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/xue/hongshu/webview/XhsWebViewClient$Companion;", "", "()V", "MAX_DELAY", "", "MIN_DELAY", "TAG", "", "TIMEOUT_DELAY", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    /**
     * SSL错误信息数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\r\n\u0002\u0010\b\n\u0002\b\u0002\b\u0082\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0007J\t\u0010\f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\r\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0005H\u00c6\u0003J\'\u0010\u000f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0010\u001a\u00020\u00052\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\tR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"}, d2 = {"Lcom/xue/hongshu/webview/XhsWebViewClient$SslErrorInfo;", "", "message", "", "canProceed", "", "isCritical", "(Ljava/lang/String;ZZ)V", "getCanProceed", "()Z", "getMessage", "()Ljava/lang/String;", "component1", "component2", "component3", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    static final class SslErrorInfo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String message = null;
        private final boolean canProceed = false;
        private final boolean isCritical = false;
        
        public SslErrorInfo(@org.jetbrains.annotations.NotNull()
        java.lang.String message, boolean canProceed, boolean isCritical) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMessage() {
            return null;
        }
        
        public final boolean getCanProceed() {
            return false;
        }
        
        public final boolean isCritical() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final boolean component2() {
            return false;
        }
        
        public final boolean component3() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.webview.XhsWebViewClient.SslErrorInfo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String message, boolean canProceed, boolean isCritical) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}