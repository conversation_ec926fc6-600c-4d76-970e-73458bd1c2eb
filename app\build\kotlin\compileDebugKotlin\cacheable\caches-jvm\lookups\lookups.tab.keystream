  SuppressLint android.annotation  Activity android.app  AppDatabase android.app.Activity  EXTRA_EMPLOYEE_ID android.app.Activity  EXTRA_XHS_USER_ID android.app.Activity  EmployeeRepository android.app.Activity  	Exception android.app.Activity  HongshuTheme android.app.Activity  Intent android.app.Activity  LogViewerScreen android.app.Activity  Logger android.app.Activity  
MainScreen android.app.Activity  
MainViewModel android.app.Activity  MainViewModelFactory android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  Surface android.app.Activity  Toast android.app.Activity  WebViewActivity android.app.Activity  
WebViewScreen android.app.Activity  android android.app.Activity  apply android.app.Activity  com android.app.Activity  createIntent android.app.Activity  e android.app.Activity  
employeeId android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  finish android.app.Activity  getDatabase android.app.Activity  getValue android.app.Activity  i android.app.Activity  init android.app.Activity  intent android.app.Activity  isEmpty android.app.Activity  java android.app.Activity  launch android.app.Activity  lifecycleScope android.app.Activity  logDataExtraction android.app.Activity  
logUserAction android.app.Activity  mutableStateOf android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  remember android.app.Activity  
repository android.app.Activity  
setContent android.app.Activity  setValue android.app.Activity  
startActivity android.app.Activity  	viewModel android.app.Activity  w android.app.Activity  Context android.content  Intent android.content  SharedPreferences android.content  AppDatabase android.content.Context  CONNECTIVITY_SERVICE android.content.Context  EXTRA_EMPLOYEE_ID android.content.Context  EXTRA_XHS_USER_ID android.content.Context  EmployeeRepository android.content.Context  	Exception android.content.Context  HongshuTheme android.content.Context  Intent android.content.Context  LogViewerScreen android.content.Context  Logger android.content.Context  MODE_PRIVATE android.content.Context  
MainScreen android.content.Context  
MainViewModel android.content.Context  MainViewModelFactory android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  Toast android.content.Context  WebViewActivity android.content.Context  
WebViewScreen android.content.Context  android android.content.Context  applicationContext android.content.Context  apply android.content.Context  com android.content.Context  createIntent android.content.Context  e android.content.Context  
employeeId android.content.Context  enableEdgeToEdge android.content.Context  filesDir android.content.Context  fillMaxSize android.content.Context  finish android.content.Context  getDatabase android.content.Context  getExternalFilesDir android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  getValue android.content.Context  i android.content.Context  init android.content.Context  isEmpty android.content.Context  java android.content.Context  launch android.content.Context  lifecycleScope android.content.Context  logDataExtraction android.content.Context  
logUserAction android.content.Context  mutableStateOf android.content.Context  provideDelegate android.content.Context  remember android.content.Context  
repository android.content.Context  
setContent android.content.Context  setValue android.content.Context  
startActivity android.content.Context  	viewModel android.content.Context  w android.content.Context  AppDatabase android.content.ContextWrapper  EXTRA_EMPLOYEE_ID android.content.ContextWrapper  EXTRA_XHS_USER_ID android.content.ContextWrapper  EmployeeRepository android.content.ContextWrapper  	Exception android.content.ContextWrapper  HongshuTheme android.content.ContextWrapper  Intent android.content.ContextWrapper  LogViewerScreen android.content.ContextWrapper  Logger android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  
MainViewModel android.content.ContextWrapper  MainViewModelFactory android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  Toast android.content.ContextWrapper  WebViewActivity android.content.ContextWrapper  
WebViewScreen android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  com android.content.ContextWrapper  createIntent android.content.ContextWrapper  e android.content.ContextWrapper  
employeeId android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  finish android.content.ContextWrapper  getDatabase android.content.ContextWrapper  getValue android.content.ContextWrapper  i android.content.ContextWrapper  init android.content.ContextWrapper  isEmpty android.content.ContextWrapper  java android.content.ContextWrapper  launch android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  logDataExtraction android.content.ContextWrapper  
logUserAction android.content.ContextWrapper  mutableStateOf android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  remember android.content.ContextWrapper  
repository android.content.ContextWrapper  
setContent android.content.ContextWrapper  setValue android.content.ContextWrapper  
startActivity android.content.ContextWrapper  	viewModel android.content.ContextWrapper  w android.content.ContextWrapper  EXTRA_EMPLOYEE_ID android.content.Intent  EXTRA_XHS_USER_ID android.content.Intent  apply android.content.Intent  getStringExtra android.content.Intent  putExtra android.content.Intent  edit !android.content.SharedPreferences  getInt !android.content.SharedPreferences  getLong !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  clear (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  putLong (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  Bitmap android.graphics  ConnectivityManager android.net  Network android.net  NetworkCapabilities android.net  NetworkRequest android.net  Uri android.net  NetworkCallback android.net.ConnectivityManager  
activeNetwork android.net.ConnectivityManager  getNetworkCapabilities android.net.ConnectivityManager  registerNetworkCallback android.net.ConnectivityManager  unregisterNetworkCallback android.net.ConnectivityManager  ConnectionQuality /android.net.ConnectivityManager.NetworkCallback  Log /android.net.ConnectivityManager.NetworkCallback  NetworkState /android.net.ConnectivityManager.NetworkCallback  TAG /android.net.ConnectivityManager.NetworkCallback  _connectionQuality /android.net.ConnectivityManager.NetworkCallback  
_networkState /android.net.ConnectivityManager.NetworkCallback  onAvailable /android.net.ConnectivityManager.NetworkCallback  onCapabilitiesChanged /android.net.ConnectivityManager.NetworkCallback  onLost /android.net.ConnectivityManager.NetworkCallback  updateConnectionQuality /android.net.ConnectivityManager.NetworkCallback  updateNetworkState /android.net.ConnectivityManager.NetworkCallback  NET_CAPABILITY_INTERNET android.net.NetworkCapabilities  TRANSPORT_CELLULAR android.net.NetworkCapabilities  TRANSPORT_ETHERNET android.net.NetworkCapabilities  TRANSPORT_WIFI android.net.NetworkCapabilities  
hasCapability android.net.NetworkCapabilities  hasTransport android.net.NetworkCapabilities  linkDownstreamBandwidthKbps android.net.NetworkCapabilities  linkUpstreamBandwidthKbps android.net.NetworkCapabilities  Builder android.net.NetworkRequest  
addCapability "android.net.NetworkRequest.Builder  build "android.net.NetworkRequest.Builder  SslError android.net.http  SSL_DATE_INVALID android.net.http.SslError  SSL_EXPIRED android.net.http.SslError  SSL_IDMISMATCH android.net.http.SslError  SSL_INVALID android.net.http.SslError  SSL_NOTYETVALID android.net.http.SslError  
SSL_UNTRUSTED android.net.http.SslError  primaryError android.net.http.SslError  toString android.net.http.SslError  url android.net.http.SslError  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  AppDatabase  android.view.ContextThemeWrapper  EXTRA_EMPLOYEE_ID  android.view.ContextThemeWrapper  EXTRA_XHS_USER_ID  android.view.ContextThemeWrapper  EmployeeRepository  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  HongshuTheme  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  LogViewerScreen  android.view.ContextThemeWrapper  Logger  android.view.ContextThemeWrapper  
MainScreen  android.view.ContextThemeWrapper  
MainViewModel  android.view.ContextThemeWrapper  MainViewModelFactory  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  WebViewActivity  android.view.ContextThemeWrapper  
WebViewScreen  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  com  android.view.ContextThemeWrapper  createIntent  android.view.ContextThemeWrapper  e  android.view.ContextThemeWrapper  
employeeId  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  getDatabase  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  i  android.view.ContextThemeWrapper  init  android.view.ContextThemeWrapper  isEmpty  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  logDataExtraction  android.view.ContextThemeWrapper  
logUserAction  android.view.ContextThemeWrapper  mutableStateOf  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  remember  android.view.ContextThemeWrapper  
repository  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  setValue  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  	viewModel  android.view.ContextThemeWrapper  w  android.view.ContextThemeWrapper  postDelayed android.view.View  SslErrorHandler android.webkit  
ValueCallback android.webkit  WebChromeClient android.webkit  WebResourceError android.webkit  WebResourceRequest android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  cancel android.webkit.SslErrorHandler  proceed android.webkit.SslErrorHandler  <SAM-CONSTRUCTOR> android.webkit.ValueCallback  description android.webkit.WebResourceError  	errorCode android.webkit.WebResourceError  url !android.webkit.WebResourceRequest  LOAD_DEFAULT android.webkit.WebSettings  MIXED_CONTENT_ALWAYS_ALLOW android.webkit.WebSettings  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  builtInZoomControls android.webkit.WebSettings  	cacheMode android.webkit.WebSettings  databaseEnabled android.webkit.WebSettings  displayZoomControls android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  loadWithOverviewMode android.webkit.WebSettings  mixedContentMode android.webkit.WebSettings  setGeolocationEnabled android.webkit.WebSettings  setSupportZoom android.webkit.WebSettings  useWideViewPort android.webkit.WebSettings  userAgentString android.webkit.WebSettings  AntiDetectionHelper android.webkit.WebView  WebView android.webkit.WebView  XhsWebViewClient android.webkit.WebView  addRandomInteractions android.webkit.WebView  apply android.webkit.WebView  bypassCommonDetection android.webkit.WebView  configureWebView android.webkit.WebView  evaluateJavascript android.webkit.WebView  getRandomDelay android.webkit.WebView  injectAntiDetectionScript android.webkit.WebView  let android.webkit.WebView  loadDataWithBaseURL android.webkit.WebView  loadUrl android.webkit.WebView  postDelayed android.webkit.WebView  reload android.webkit.WebView  setWebContentsDebuggingEnabled android.webkit.WebView  settings android.webkit.WebView  simulateHumanScroll android.webkit.WebView  simulateMouseMovement android.webkit.WebView  webChromeClient android.webkit.WebView  
webViewClient android.webkit.WebView  CoroutineScope android.webkit.WebViewClient  Dispatchers android.webkit.WebViewClient  
ERROR_BAD_URL android.webkit.WebViewClient  
ERROR_CONNECT android.webkit.WebViewClient  ERROR_FAILED_SSL_HANDSHAKE android.webkit.WebViewClient  ERROR_HOST_LOOKUP android.webkit.WebViewClient  
ERROR_TIMEOUT android.webkit.WebViewClient  ErrorHandler android.webkit.WebViewClient  	Exception android.webkit.WebViewClient  Log android.webkit.WebViewClient  	MAX_DELAY android.webkit.WebViewClient  	MIN_DELAY android.webkit.WebViewClient  Random android.webkit.WebViewClient  Regex android.webkit.WebViewClient  SslError android.webkit.WebViewClient  System android.webkit.WebViewClient  TAG android.webkit.WebViewClient  
TIMEOUT_DELAY android.webkit.WebViewClient  XhsUserData android.webkit.WebViewClient  analyzeError android.webkit.WebViewClient  android android.webkit.WebViewClient  calculateRetryDelay android.webkit.WebViewClient  contains android.webkit.WebViewClient  delay android.webkit.WebViewClient  extractUserData android.webkit.WebViewClient  getUserFriendlyMessage android.webkit.WebViewClient  handleError android.webkit.WebViewClient  invoke android.webkit.WebViewClient  isEmpty android.webkit.WebViewClient  isExtracting android.webkit.WebViewClient  kotlinx android.webkit.WebViewClient  launch android.webkit.WebViewClient  nextLong android.webkit.WebViewClient  onPageFinished android.webkit.WebViewClient  
onPageStarted android.webkit.WebViewClient  onReceivedError android.webkit.WebViewClient  recordError android.webkit.WebViewClient  replace android.webkit.WebViewClient  
retryCount android.webkit.WebViewClient  trim android.webkit.WebViewClient  
trimIndent android.webkit.WebViewClient  Toast android.widget  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  AppDatabase #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  EXTRA_EMPLOYEE_ID #androidx.activity.ComponentActivity  EXTRA_XHS_USER_ID #androidx.activity.ComponentActivity  EmployeeRepository #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  HongshuTheme #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  LogViewerScreen #androidx.activity.ComponentActivity  Logger #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  
MainViewModel #androidx.activity.ComponentActivity  MainViewModelFactory #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  WebViewActivity #androidx.activity.ComponentActivity  
WebViewScreen #androidx.activity.ComponentActivity  XhsUserData #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  com #androidx.activity.ComponentActivity  createIntent #androidx.activity.ComponentActivity  e #androidx.activity.ComponentActivity  
employeeId #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  getDatabase #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  i #androidx.activity.ComponentActivity  init #androidx.activity.ComponentActivity  isEmpty #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  logDataExtraction #androidx.activity.ComponentActivity  
logUserAction #androidx.activity.ComponentActivity  mutableStateOf #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  remember #androidx.activity.ComponentActivity  
repository #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  setValue #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  	viewModel #androidx.activity.ComponentActivity  w #androidx.activity.ComponentActivity  AppDatabase -androidx.activity.ComponentActivity.Companion  EXTRA_EMPLOYEE_ID -androidx.activity.ComponentActivity.Companion  EXTRA_XHS_USER_ID -androidx.activity.ComponentActivity.Companion  EmployeeRepository -androidx.activity.ComponentActivity.Companion  HongshuTheme -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  LogViewerScreen -androidx.activity.ComponentActivity.Companion  Logger -androidx.activity.ComponentActivity.Companion  
MainScreen -androidx.activity.ComponentActivity.Companion  MainViewModelFactory -androidx.activity.ComponentActivity.Companion  
MaterialTheme -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  Surface -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  WebViewActivity -androidx.activity.ComponentActivity.Companion  
WebViewScreen -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  com -androidx.activity.ComponentActivity.Companion  createIntent -androidx.activity.ComponentActivity.Companion  e -androidx.activity.ComponentActivity.Companion  
employeeId -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  finish -androidx.activity.ComponentActivity.Companion  getDatabase -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  i -androidx.activity.ComponentActivity.Companion  init -androidx.activity.ComponentActivity.Companion  isEmpty -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  launch -androidx.activity.ComponentActivity.Companion  lifecycleScope -androidx.activity.ComponentActivity.Companion  logDataExtraction -androidx.activity.ComponentActivity.Companion  
logUserAction -androidx.activity.ComponentActivity.Companion  mutableStateOf -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  remember -androidx.activity.ComponentActivity.Companion  
repository -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  setValue -androidx.activity.ComponentActivity.Companion  
startActivity -androidx.activity.ComponentActivity.Companion  	viewModel -androidx.activity.ComponentActivity.Companion  w -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  AndroidView "androidx.compose.foundation.layout  AntiDetectionHelper "androidx.compose.foundation.layout  AppDatabase "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
AssistChip "androidx.compose.foundation.layout  AssistChipDefaults "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  
ConfigManager "androidx.compose.foundation.layout  ConnectionQuality "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  Date "androidx.compose.foundation.layout  
DetailSection "androidx.compose.foundation.layout  DisposableEffect "androidx.compose.foundation.layout  EXTRA_EMPLOYEE_ID "androidx.compose.foundation.layout  EXTRA_XHS_USER_ID "androidx.compose.foundation.layout  EmployeeCard "androidx.compose.foundation.layout  EmployeeRepository "androidx.compose.foundation.layout  EmployeeStatus "androidx.compose.foundation.layout  EmployeeWithStats "androidx.compose.foundation.layout  ErrorHandler "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
FilterChip "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  
FontFamily "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  HongshuTheme "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  Intent "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LinearProgressIndicator "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  LogFilterBar "androidx.compose.foundation.layout  LogItem "androidx.compose.foundation.layout  LogStats "androidx.compose.foundation.layout  Logger "androidx.compose.foundation.layout  Long "androidx.compose.foundation.layout  
MainViewModel "androidx.compose.foundation.layout  Map "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  NetworkState "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Pair "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  
StatisticItem "androidx.compose.foundation.layout  
Statistics "androidx.compose.foundation.layout  StatisticsCard "androidx.compose.foundation.layout  
StatusChip "androidx.compose.foundation.layout  
StatusItem "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  SuppressLint "androidx.compose.foundation.layout  SystemMonitorCard "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  Toast "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  WebView "androidx.compose.foundation.layout  WebViewActivity "androidx.compose.foundation.layout  
WebViewScreen "androidx.compose.foundation.layout  
WebViewTester "androidx.compose.foundation.layout  XhsUserData "androidx.compose.foundation.layout  XhsWebViewClient "androidx.compose.foundation.layout  addRandomInteractions "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  assistChipColors "androidx.compose.foundation.layout  bypassCommonDetection "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  clearErrorStats "androidx.compose.foundation.layout  	clearLogs "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  
component1 "androidx.compose.foundation.layout  
component2 "androidx.compose.foundation.layout  configureWebView "androidx.compose.foundation.layout  contains "androidx.compose.foundation.layout  e "androidx.compose.foundation.layout  
employeeId "androidx.compose.foundation.layout  
exportLogs "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  filter "androidx.compose.foundation.layout  finish "androidx.compose.foundation.layout  first "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  
formatTime "androidx.compose.foundation.layout  getConnectionQualityText "androidx.compose.foundation.layout  getDatabase "androidx.compose.foundation.layout  getDetectionLevelColor "androidx.compose.foundation.layout  getDetectionLevelText "androidx.compose.foundation.layout  
getErrorStats "androidx.compose.foundation.layout  getErrorTypeText "androidx.compose.foundation.layout  
getLogContent "androidx.compose.foundation.layout  getLogStats "androidx.compose.foundation.layout  getNetworkColor "androidx.compose.foundation.layout  getNetworkDisplayText "androidx.compose.foundation.layout  getRandomDelay "androidx.compose.foundation.layout  getStrategyModeText "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  injectAntiDetectionScript "androidx.compose.foundation.layout  isBlank "androidx.compose.foundation.layout  isEmpty "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  
isNullOrEmpty "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  kotlinx "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  lines "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  logDataExtraction "androidx.compose.foundation.layout  map "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  
repository "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  simulateHumanScroll "androidx.compose.foundation.layout  simulateMouseMovement "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  testBasicWebView "androidx.compose.foundation.layout  testJavaScriptExecution "androidx.compose.foundation.layout  
testXhsAccess "androidx.compose.foundation.layout  to "androidx.compose.foundation.layout  trim "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  End .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  AndroidView +androidx.compose.foundation.layout.BoxScope  AntiDetectionHelper +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  LinearProgressIndicator +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  WebView +androidx.compose.foundation.layout.BoxScope  XhsWebViewClient +androidx.compose.foundation.layout.BoxScope  addRandomInteractions +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  bypassCommonDetection +androidx.compose.foundation.layout.BoxScope  
cardElevation +androidx.compose.foundation.layout.BoxScope  configureWebView +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  getRandomDelay +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  injectAntiDetectionScript +androidx.compose.foundation.layout.BoxScope  isEmpty +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  simulateHumanScroll +androidx.compose.foundation.layout.BoxScope  simulateMouseMovement +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  
DetailSection .androidx.compose.foundation.layout.ColumnScope  EmployeeCard .androidx.compose.foundation.layout.ColumnScope  EmployeeStatus .androidx.compose.foundation.layout.ColumnScope  ErrorHandler .androidx.compose.foundation.layout.ColumnScope  
FilterChip .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Info .androidx.compose.foundation.layout.ColumnScope  Int .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  LaunchedEffect .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  LogFilterBar .androidx.compose.foundation.layout.ColumnScope  LogItem .androidx.compose.foundation.layout.ColumnScope  Logger .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  
PaddingValues .androidx.compose.foundation.layout.ColumnScope  Refresh .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  
StatisticItem .androidx.compose.foundation.layout.ColumnScope  StatisticsCard .androidx.compose.foundation.layout.ColumnScope  
StatusChip .androidx.compose.foundation.layout.ColumnScope  
StatusItem .androidx.compose.foundation.layout.ColumnScope  SystemMonitorCard .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  Warning .androidx.compose.foundation.layout.ColumnScope  
WebViewTester .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  clearErrorStats .androidx.compose.foundation.layout.ColumnScope  	clearLogs .androidx.compose.foundation.layout.ColumnScope  
component1 .androidx.compose.foundation.layout.ColumnScope  
component2 .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  first .androidx.compose.foundation.layout.ColumnScope  
formatTime .androidx.compose.foundation.layout.ColumnScope  getConnectionQualityText .androidx.compose.foundation.layout.ColumnScope  getDetectionLevelColor .androidx.compose.foundation.layout.ColumnScope  getDetectionLevelText .androidx.compose.foundation.layout.ColumnScope  
getErrorStats .androidx.compose.foundation.layout.ColumnScope  getErrorTypeText .androidx.compose.foundation.layout.ColumnScope  getLogStats .androidx.compose.foundation.layout.ColumnScope  getNetworkColor .androidx.compose.foundation.layout.ColumnScope  getNetworkDisplayText .androidx.compose.foundation.layout.ColumnScope  getStrategyModeText .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  heightIn .androidx.compose.foundation.layout.ColumnScope  isBlank .androidx.compose.foundation.layout.ColumnScope  isEmpty .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  
isNullOrEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  map .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  testBasicWebView .androidx.compose.foundation.layout.ColumnScope  testJavaScriptExecution .androidx.compose.foundation.layout.ColumnScope  
testXhsAccess .androidx.compose.foundation.layout.ColumnScope  to .androidx.compose.foundation.layout.ColumnScope  trim .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  DetectionLevel 0androidx.compose.foundation.layout.ConfigManager  StrategyMode 0androidx.compose.foundation.layout.ConfigManager  	ErrorType /androidx.compose.foundation.layout.ErrorHandler  Button +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Clear +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  
FilterChip +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  Info +androidx.compose.foundation.layout.RowScope  Int +androidx.compose.foundation.layout.RowScope  Logger +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  Share +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  
StatisticItem +androidx.compose.foundation.layout.RowScope  
StatusChip +androidx.compose.foundation.layout.RowScope  
StatusItem +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  Warning +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  
exportLogs +androidx.compose.foundation.layout.RowScope  
formatTime +androidx.compose.foundation.layout.RowScope  getDetectionLevelColor +androidx.compose.foundation.layout.RowScope  getDetectionLevelText +androidx.compose.foundation.layout.RowScope  getNetworkColor +androidx.compose.foundation.layout.RowScope  getNetworkDisplayText +androidx.compose.foundation.layout.RowScope  isBlank +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  to +androidx.compose.foundation.layout.RowScope  trim +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  webkit *androidx.compose.foundation.layout.android  WebChromeClient 1androidx.compose.foundation.layout.android.webkit  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  graphics 6androidx.compose.foundation.layout.androidx.compose.ui  Color ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  xue &androidx.compose.foundation.layout.com  hongshu *androidx.compose.foundation.layout.com.xue  utils 2androidx.compose.foundation.layout.com.xue.hongshu  LogStats 8androidx.compose.foundation.layout.com.xue.hongshu.utils  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  
LazyListState  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  rememberLazyListState  androidx.compose.foundation.lazy  
DetailSection .androidx.compose.foundation.lazy.LazyItemScope  EmployeeCard .androidx.compose.foundation.lazy.LazyItemScope  LogItem .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  
component1 .androidx.compose.foundation.lazy.LazyItemScope  
component2 .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getConnectionQualityText .androidx.compose.foundation.lazy.LazyItemScope  getDetectionLevelText .androidx.compose.foundation.lazy.LazyItemScope  getErrorTypeText .androidx.compose.foundation.lazy.LazyItemScope  getNetworkDisplayText .androidx.compose.foundation.lazy.LazyItemScope  getStrategyModeText .androidx.compose.foundation.lazy.LazyItemScope  listOf .androidx.compose.foundation.lazy.LazyItemScope  map .androidx.compose.foundation.lazy.LazyItemScope  to .androidx.compose.foundation.lazy.LazyItemScope  
DetailSection .androidx.compose.foundation.lazy.LazyListScope  EmployeeCard .androidx.compose.foundation.lazy.LazyListScope  LogItem .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  
component1 .androidx.compose.foundation.lazy.LazyListScope  
component2 .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getConnectionQualityText .androidx.compose.foundation.lazy.LazyListScope  getDetectionLevelText .androidx.compose.foundation.lazy.LazyListScope  getErrorTypeText .androidx.compose.foundation.lazy.LazyListScope  getNetworkDisplayText .androidx.compose.foundation.lazy.LazyListScope  getStrategyModeText .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  listOf .androidx.compose.foundation.lazy.LazyListScope  map .androidx.compose.foundation.lazy.LazyListScope  to .androidx.compose.foundation.lazy.LazyListScope  animateScrollToItem .androidx.compose.foundation.lazy.LazyListState  KeyboardOptions  androidx.compose.foundation.text  SelectionContainer *androidx.compose.foundation.text.selection  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  Clear ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Share ,androidx.compose.material.icons.Icons.Filled  Warning ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  Clear &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Share &androidx.compose.material.icons.filled  Warning &androidx.compose.material.icons.filled  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  AndroidView androidx.compose.material3  AntiDetectionHelper androidx.compose.material3  AppDatabase androidx.compose.material3  Arrangement androidx.compose.material3  
AssistChip androidx.compose.material3  AssistChipDefaults androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  
ChipColors androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  
ConfigManager androidx.compose.material3  ConnectionQuality androidx.compose.material3  Context androidx.compose.material3  Date androidx.compose.material3  
DetailSection androidx.compose.material3  DisposableEffect androidx.compose.material3  EXTRA_EMPLOYEE_ID androidx.compose.material3  EXTRA_XHS_USER_ID androidx.compose.material3  EmployeeCard androidx.compose.material3  EmployeeRepository androidx.compose.material3  EmployeeStatus androidx.compose.material3  EmployeeWithStats androidx.compose.material3  ErrorHandler androidx.compose.material3  	Exception androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
FilterChip androidx.compose.material3  FloatingActionButton androidx.compose.material3  
FontFamily androidx.compose.material3  
FontWeight androidx.compose.material3  HongshuTheme androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Int androidx.compose.material3  Intent androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  List androidx.compose.material3  Locale androidx.compose.material3  LogFilterBar androidx.compose.material3  LogItem androidx.compose.material3  LogStats androidx.compose.material3  Logger androidx.compose.material3  Long androidx.compose.material3  
MainViewModel androidx.compose.material3  Map androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  NetworkState androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  
PaddingValues androidx.compose.material3  Pair androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Spacer androidx.compose.material3  
StatisticItem androidx.compose.material3  
Statistics androidx.compose.material3  StatisticsCard androidx.compose.material3  
StatusChip androidx.compose.material3  
StatusItem androidx.compose.material3  String androidx.compose.material3  SuppressLint androidx.compose.material3  Surface androidx.compose.material3  SystemMonitorCard androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  Toast androidx.compose.material3  	TopAppBar androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  WebView androidx.compose.material3  WebViewActivity androidx.compose.material3  
WebViewScreen androidx.compose.material3  
WebViewTester androidx.compose.material3  XhsUserData androidx.compose.material3  XhsWebViewClient androidx.compose.material3  addRandomInteractions androidx.compose.material3  android androidx.compose.material3  androidx androidx.compose.material3  apply androidx.compose.material3  assistChipColors androidx.compose.material3  bypassCommonDetection androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  clearErrorStats androidx.compose.material3  	clearLogs androidx.compose.material3  com androidx.compose.material3  
component1 androidx.compose.material3  
component2 androidx.compose.material3  configureWebView androidx.compose.material3  contains androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  e androidx.compose.material3  
employeeId androidx.compose.material3  
exportLogs androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  filter androidx.compose.material3  finish androidx.compose.material3  first androidx.compose.material3  forEach androidx.compose.material3  
formatTime androidx.compose.material3  getConnectionQualityText androidx.compose.material3  getDatabase androidx.compose.material3  getDetectionLevelColor androidx.compose.material3  getDetectionLevelText androidx.compose.material3  
getErrorStats androidx.compose.material3  getErrorTypeText androidx.compose.material3  
getLogContent androidx.compose.material3  getLogStats androidx.compose.material3  getNetworkColor androidx.compose.material3  getNetworkDisplayText androidx.compose.material3  getRandomDelay androidx.compose.material3  getStrategyModeText androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  heightIn androidx.compose.material3  injectAntiDetectionScript androidx.compose.material3  isBlank androidx.compose.material3  isEmpty androidx.compose.material3  
isNotEmpty androidx.compose.material3  
isNullOrEmpty androidx.compose.material3  java androidx.compose.material3  kotlinx androidx.compose.material3  launch androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  lines androidx.compose.material3  listOf androidx.compose.material3  logDataExtraction androidx.compose.material3  map androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  
repository androidx.compose.material3  setValue androidx.compose.material3  simulateHumanScroll androidx.compose.material3  simulateMouseMovement androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  testBasicWebView androidx.compose.material3  testJavaScriptExecution androidx.compose.material3  
testXhsAccess androidx.compose.material3  to androidx.compose.material3  trim androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  assistChipColors -androidx.compose.material3.AssistChipDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  	secondary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  tertiary &androidx.compose.material3.ColorScheme  DetectionLevel (androidx.compose.material3.ConfigManager  StrategyMode (androidx.compose.material3.ConfigManager  	ErrorType 'androidx.compose.material3.ErrorHandler  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
titleSmall %androidx.compose.material3.Typography  webkit "androidx.compose.material3.android  WebChromeClient )androidx.compose.material3.android.webkit  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  graphics .androidx.compose.material3.androidx.compose.ui  Color 7androidx.compose.material3.androidx.compose.ui.graphics  xue androidx.compose.material3.com  hongshu "androidx.compose.material3.com.xue  utils *androidx.compose.material3.com.xue.hongshu  LogStats 0androidx.compose.material3.com.xue.hongshu.utils  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  AndroidView androidx.compose.runtime  AntiDetectionHelper androidx.compose.runtime  AppDatabase androidx.compose.runtime  Arrangement androidx.compose.runtime  
AssistChip androidx.compose.runtime  AssistChipDefaults androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  
ConfigManager androidx.compose.runtime  ConnectionQuality androidx.compose.runtime  Context androidx.compose.runtime  Date androidx.compose.runtime  
DetailSection androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  EXTRA_EMPLOYEE_ID androidx.compose.runtime  EXTRA_XHS_USER_ID androidx.compose.runtime  EmployeeCard androidx.compose.runtime  EmployeeRepository androidx.compose.runtime  EmployeeStatus androidx.compose.runtime  EmployeeWithStats androidx.compose.runtime  ErrorHandler androidx.compose.runtime  	Exception androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
FilterChip androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  
FontFamily androidx.compose.runtime  
FontWeight androidx.compose.runtime  HongshuTheme androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Int androidx.compose.runtime  Intent androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LinearProgressIndicator androidx.compose.runtime  List androidx.compose.runtime  Locale androidx.compose.runtime  LogFilterBar androidx.compose.runtime  LogItem androidx.compose.runtime  LogStats androidx.compose.runtime  LogViewerScreen androidx.compose.runtime  Logger androidx.compose.runtime  Long androidx.compose.runtime  
MainScreen androidx.compose.runtime  
MainViewModel androidx.compose.runtime  MainViewModelFactory androidx.compose.runtime  Map androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  NetworkState androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  
PaddingValues androidx.compose.runtime  Pair androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  StatisticsCard androidx.compose.runtime  
StatusChip androidx.compose.runtime  
StatusItem androidx.compose.runtime  String androidx.compose.runtime  SuppressLint androidx.compose.runtime  Surface androidx.compose.runtime  SystemMonitorCard androidx.compose.runtime  Text androidx.compose.runtime  
TextButton androidx.compose.runtime  Toast androidx.compose.runtime  	TopAppBar androidx.compose.runtime  Unit androidx.compose.runtime  WebView androidx.compose.runtime  WebViewActivity androidx.compose.runtime  
WebViewScreen androidx.compose.runtime  
WebViewTester androidx.compose.runtime  XhsUserData androidx.compose.runtime  XhsWebViewClient androidx.compose.runtime  addRandomInteractions androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  apply androidx.compose.runtime  assistChipColors androidx.compose.runtime  bypassCommonDetection androidx.compose.runtime  
cardColors androidx.compose.runtime  
cardElevation androidx.compose.runtime  clearErrorStats androidx.compose.runtime  	clearLogs androidx.compose.runtime  com androidx.compose.runtime  
component1 androidx.compose.runtime  
component2 androidx.compose.runtime  configureWebView androidx.compose.runtime  contains androidx.compose.runtime  createIntent androidx.compose.runtime  e androidx.compose.runtime  
employeeId androidx.compose.runtime  
exportLogs androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  filter androidx.compose.runtime  finish androidx.compose.runtime  first androidx.compose.runtime  forEach androidx.compose.runtime  
formatTime androidx.compose.runtime  getConnectionQualityText androidx.compose.runtime  getDatabase androidx.compose.runtime  getDetectionLevelColor androidx.compose.runtime  getDetectionLevelText androidx.compose.runtime  
getErrorStats androidx.compose.runtime  getErrorTypeText androidx.compose.runtime  
getLogContent androidx.compose.runtime  getLogStats androidx.compose.runtime  getNetworkColor androidx.compose.runtime  getNetworkDisplayText androidx.compose.runtime  getRandomDelay androidx.compose.runtime  getStrategyModeText androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  heightIn androidx.compose.runtime  i androidx.compose.runtime  init androidx.compose.runtime  injectAntiDetectionScript androidx.compose.runtime  isBlank androidx.compose.runtime  isEmpty androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  
isNullOrEmpty androidx.compose.runtime  java androidx.compose.runtime  kotlinx androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  lines androidx.compose.runtime  listOf androidx.compose.runtime  logDataExtraction androidx.compose.runtime  
logUserAction androidx.compose.runtime  map androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  
repository androidx.compose.runtime  setValue androidx.compose.runtime  simulateHumanScroll androidx.compose.runtime  simulateMouseMovement androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  
startActivity androidx.compose.runtime  testBasicWebView androidx.compose.runtime  testJavaScriptExecution androidx.compose.runtime  
testXhsAccess androidx.compose.runtime  to androidx.compose.runtime  trim androidx.compose.runtime  	viewModel androidx.compose.runtime  w androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  DetectionLevel &androidx.compose.runtime.ConfigManager  StrategyMode &androidx.compose.runtime.ConfigManager  	onDispose .androidx.compose.runtime.DisposableEffectScope  	ErrorType %androidx.compose.runtime.ErrorHandler  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  webkit  androidx.compose.runtime.android  WebChromeClient 'androidx.compose.runtime.android.webkit  xue androidx.compose.runtime.com  hongshu  androidx.compose.runtime.com.xue  utils (androidx.compose.runtime.com.xue.hongshu  LogStats .androidx.compose.runtime.com.xue.hongshu.utils  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  heightIn androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  heightIn &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  to "androidx.compose.ui.graphics.Color  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  copy "androidx.compose.ui.text.TextStyle  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  	Monospace (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  	Companion +androidx.compose.ui.text.input.KeyboardType  Text +androidx.compose.ui.text.input.KeyboardType  Text 5androidx.compose.ui.text.input.KeyboardType.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Dialog androidx.compose.ui.window  AppDatabase #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  EXTRA_EMPLOYEE_ID #androidx.core.app.ComponentActivity  EXTRA_XHS_USER_ID #androidx.core.app.ComponentActivity  EmployeeRepository #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  HongshuTheme #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  LogViewerScreen #androidx.core.app.ComponentActivity  Logger #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  
MainViewModel #androidx.core.app.ComponentActivity  MainViewModelFactory #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  WebViewActivity #androidx.core.app.ComponentActivity  
WebViewScreen #androidx.core.app.ComponentActivity  XhsUserData #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  com #androidx.core.app.ComponentActivity  createIntent #androidx.core.app.ComponentActivity  e #androidx.core.app.ComponentActivity  
employeeId #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  getDatabase #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  i #androidx.core.app.ComponentActivity  init #androidx.core.app.ComponentActivity  isEmpty #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  logDataExtraction #androidx.core.app.ComponentActivity  
logUserAction #androidx.core.app.ComponentActivity  mutableStateOf #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  remember #androidx.core.app.ComponentActivity  
repository #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  setValue #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  	viewModel #androidx.core.app.ComponentActivity  w #androidx.core.app.ComponentActivity  LifecycleCoroutineScope androidx.lifecycle  	ViewModel androidx.lifecycle  ViewModelProvider androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  launch *androidx.lifecycle.LifecycleCoroutineScope  Factory $androidx.lifecycle.ViewModelProvider  collectAsStateWithLifecycle androidx.lifecycle.compose  	viewModel $androidx.lifecycle.viewmodel.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Employee 
androidx.room  EmployeeStatus 
androidx.room  Entity 
androidx.room  Flow 
androidx.room  Insert 
androidx.room  Int 
androidx.room  List 
androidx.room  Long 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  String 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  AppDatabase androidx.room.RoomDatabase  Builder androidx.room.RoomDatabase  	Companion androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  EmployeeDao androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  databaseBuilder androidx.room.RoomDatabase  java androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  AppDatabase $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  databaseBuilder $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  AppDatabase com.xue.hongshu  Bundle com.xue.hongshu  ComponentActivity com.xue.hongshu  EmployeeRepository com.xue.hongshu  	Exception com.xue.hongshu  HongshuTheme com.xue.hongshu  LogViewerScreen com.xue.hongshu  Logger com.xue.hongshu  MainActivity com.xue.hongshu  
MainScreen com.xue.hongshu  
MainViewModel com.xue.hongshu  MainViewModelFactory com.xue.hongshu  
MaterialTheme com.xue.hongshu  Modifier com.xue.hongshu  String com.xue.hongshu  Surface com.xue.hongshu  android com.xue.hongshu  com com.xue.hongshu  createIntent com.xue.hongshu  e com.xue.hongshu  fillMaxSize com.xue.hongshu  getDatabase com.xue.hongshu  getValue com.xue.hongshu  i com.xue.hongshu  init com.xue.hongshu  launch com.xue.hongshu  
logUserAction com.xue.hongshu  mutableStateOf com.xue.hongshu  provideDelegate com.xue.hongshu  remember com.xue.hongshu  
repository com.xue.hongshu  setValue com.xue.hongshu  
startActivity com.xue.hongshu  	viewModel com.xue.hongshu  w com.xue.hongshu  AppDatabase com.xue.hongshu.MainActivity  EmployeeRepository com.xue.hongshu.MainActivity  HongshuTheme com.xue.hongshu.MainActivity  LogViewerScreen com.xue.hongshu.MainActivity  Logger com.xue.hongshu.MainActivity  
MainScreen com.xue.hongshu.MainActivity  MainViewModelFactory com.xue.hongshu.MainActivity  
MaterialTheme com.xue.hongshu.MainActivity  Modifier com.xue.hongshu.MainActivity  Surface com.xue.hongshu.MainActivity  android com.xue.hongshu.MainActivity  com com.xue.hongshu.MainActivity  createIntent com.xue.hongshu.MainActivity  database com.xue.hongshu.MainActivity  e com.xue.hongshu.MainActivity  enableEdgeToEdge com.xue.hongshu.MainActivity  fillMaxSize com.xue.hongshu.MainActivity  getDatabase com.xue.hongshu.MainActivity  getValue com.xue.hongshu.MainActivity  i com.xue.hongshu.MainActivity  init com.xue.hongshu.MainActivity  launch com.xue.hongshu.MainActivity  lifecycleScope com.xue.hongshu.MainActivity  
logUserAction com.xue.hongshu.MainActivity  mutableStateOf com.xue.hongshu.MainActivity  provideDelegate com.xue.hongshu.MainActivity  remember com.xue.hongshu.MainActivity  
repository com.xue.hongshu.MainActivity  
setContent com.xue.hongshu.MainActivity  setValue com.xue.hongshu.MainActivity  
startActivity com.xue.hongshu.MainActivity  startWebViewActivity com.xue.hongshu.MainActivity  	viewModel com.xue.hongshu.MainActivity  w com.xue.hongshu.MainActivity  	Alignment com.xue.hongshu.activity  AndroidView com.xue.hongshu.activity  AntiDetectionHelper com.xue.hongshu.activity  AppDatabase com.xue.hongshu.activity  Box com.xue.hongshu.activity  Bundle com.xue.hongshu.activity  Card com.xue.hongshu.activity  CardDefaults com.xue.hongshu.activity  CircularProgressIndicator com.xue.hongshu.activity  Column com.xue.hongshu.activity  ComponentActivity com.xue.hongshu.activity  
Composable com.xue.hongshu.activity  Context com.xue.hongshu.activity  EXTRA_EMPLOYEE_ID com.xue.hongshu.activity  EXTRA_XHS_USER_ID com.xue.hongshu.activity  EmployeeRepository com.xue.hongshu.activity  	Exception com.xue.hongshu.activity  ExperimentalMaterial3Api com.xue.hongshu.activity  HongshuTheme com.xue.hongshu.activity  Icon com.xue.hongshu.activity  
IconButton com.xue.hongshu.activity  Icons com.xue.hongshu.activity  Int com.xue.hongshu.activity  Intent com.xue.hongshu.activity  LaunchedEffect com.xue.hongshu.activity  LinearProgressIndicator com.xue.hongshu.activity  Logger com.xue.hongshu.activity  
MaterialTheme com.xue.hongshu.activity  Modifier com.xue.hongshu.activity  OptIn com.xue.hongshu.activity  Scaffold com.xue.hongshu.activity  Spacer com.xue.hongshu.activity  String com.xue.hongshu.activity  SuppressLint com.xue.hongshu.activity  Text com.xue.hongshu.activity  Toast com.xue.hongshu.activity  	TopAppBar com.xue.hongshu.activity  Unit com.xue.hongshu.activity  WebView com.xue.hongshu.activity  WebViewActivity com.xue.hongshu.activity  
WebViewScreen com.xue.hongshu.activity  XhsUserData com.xue.hongshu.activity  XhsWebViewClient com.xue.hongshu.activity  addRandomInteractions com.xue.hongshu.activity  android com.xue.hongshu.activity  androidx com.xue.hongshu.activity  apply com.xue.hongshu.activity  bypassCommonDetection com.xue.hongshu.activity  
cardElevation com.xue.hongshu.activity  configureWebView com.xue.hongshu.activity  e com.xue.hongshu.activity  
employeeId com.xue.hongshu.activity  fillMaxSize com.xue.hongshu.activity  fillMaxWidth com.xue.hongshu.activity  finish com.xue.hongshu.activity  getDatabase com.xue.hongshu.activity  getRandomDelay com.xue.hongshu.activity  getValue com.xue.hongshu.activity  height com.xue.hongshu.activity  injectAntiDetectionScript com.xue.hongshu.activity  isEmpty com.xue.hongshu.activity  
isNotEmpty com.xue.hongshu.activity  java com.xue.hongshu.activity  launch com.xue.hongshu.activity  let com.xue.hongshu.activity  logDataExtraction com.xue.hongshu.activity  mutableStateOf com.xue.hongshu.activity  padding com.xue.hongshu.activity  provideDelegate com.xue.hongshu.activity  remember com.xue.hongshu.activity  
repository com.xue.hongshu.activity  setValue com.xue.hongshu.activity  simulateHumanScroll com.xue.hongshu.activity  simulateMouseMovement com.xue.hongshu.activity  AppDatabase (com.xue.hongshu.activity.WebViewActivity  Bundle (com.xue.hongshu.activity.WebViewActivity  	Companion (com.xue.hongshu.activity.WebViewActivity  Context (com.xue.hongshu.activity.WebViewActivity  EXTRA_EMPLOYEE_ID (com.xue.hongshu.activity.WebViewActivity  EXTRA_XHS_USER_ID (com.xue.hongshu.activity.WebViewActivity  EmployeeRepository (com.xue.hongshu.activity.WebViewActivity  	Exception (com.xue.hongshu.activity.WebViewActivity  HongshuTheme (com.xue.hongshu.activity.WebViewActivity  Intent (com.xue.hongshu.activity.WebViewActivity  Logger (com.xue.hongshu.activity.WebViewActivity  String (com.xue.hongshu.activity.WebViewActivity  Toast (com.xue.hongshu.activity.WebViewActivity  WebViewActivity (com.xue.hongshu.activity.WebViewActivity  
WebViewScreen (com.xue.hongshu.activity.WebViewActivity  XhsUserData (com.xue.hongshu.activity.WebViewActivity  apply (com.xue.hongshu.activity.WebViewActivity  createIntent (com.xue.hongshu.activity.WebViewActivity  e (com.xue.hongshu.activity.WebViewActivity  
employeeId (com.xue.hongshu.activity.WebViewActivity  finish (com.xue.hongshu.activity.WebViewActivity  getDatabase (com.xue.hongshu.activity.WebViewActivity  handleDataExtracted (com.xue.hongshu.activity.WebViewActivity  handleError (com.xue.hongshu.activity.WebViewActivity  intent (com.xue.hongshu.activity.WebViewActivity  isEmpty (com.xue.hongshu.activity.WebViewActivity  java (com.xue.hongshu.activity.WebViewActivity  launch (com.xue.hongshu.activity.WebViewActivity  lifecycleScope (com.xue.hongshu.activity.WebViewActivity  logDataExtraction (com.xue.hongshu.activity.WebViewActivity  
repository (com.xue.hongshu.activity.WebViewActivity  
setContent (com.xue.hongshu.activity.WebViewActivity  	xhsUserId (com.xue.hongshu.activity.WebViewActivity  AppDatabase 2com.xue.hongshu.activity.WebViewActivity.Companion  EXTRA_EMPLOYEE_ID 2com.xue.hongshu.activity.WebViewActivity.Companion  EXTRA_XHS_USER_ID 2com.xue.hongshu.activity.WebViewActivity.Companion  EmployeeRepository 2com.xue.hongshu.activity.WebViewActivity.Companion  HongshuTheme 2com.xue.hongshu.activity.WebViewActivity.Companion  Intent 2com.xue.hongshu.activity.WebViewActivity.Companion  Logger 2com.xue.hongshu.activity.WebViewActivity.Companion  Toast 2com.xue.hongshu.activity.WebViewActivity.Companion  WebViewActivity 2com.xue.hongshu.activity.WebViewActivity.Companion  
WebViewScreen 2com.xue.hongshu.activity.WebViewActivity.Companion  apply 2com.xue.hongshu.activity.WebViewActivity.Companion  createIntent 2com.xue.hongshu.activity.WebViewActivity.Companion  e 2com.xue.hongshu.activity.WebViewActivity.Companion  
employeeId 2com.xue.hongshu.activity.WebViewActivity.Companion  finish 2com.xue.hongshu.activity.WebViewActivity.Companion  getDatabase 2com.xue.hongshu.activity.WebViewActivity.Companion  isEmpty 2com.xue.hongshu.activity.WebViewActivity.Companion  java 2com.xue.hongshu.activity.WebViewActivity.Companion  launch 2com.xue.hongshu.activity.WebViewActivity.Companion  lifecycleScope 2com.xue.hongshu.activity.WebViewActivity.Companion  logDataExtraction 2com.xue.hongshu.activity.WebViewActivity.Companion  
repository 2com.xue.hongshu.activity.WebViewActivity.Companion  
setContent 2com.xue.hongshu.activity.WebViewActivity.Companion  webkit  com.xue.hongshu.activity.android  WebChromeClient 'com.xue.hongshu.activity.android.webkit  Dao com.xue.hongshu.data.dao  Delete com.xue.hongshu.data.dao  Employee com.xue.hongshu.data.dao  EmployeeDao com.xue.hongshu.data.dao  EmployeeStatus com.xue.hongshu.data.dao  Flow com.xue.hongshu.data.dao  Insert com.xue.hongshu.data.dao  Int com.xue.hongshu.data.dao  List com.xue.hongshu.data.dao  Long com.xue.hongshu.data.dao  OnConflictStrategy com.xue.hongshu.data.dao  Query com.xue.hongshu.data.dao  String com.xue.hongshu.data.dao  Update com.xue.hongshu.data.dao  OnConflictStrategy $com.xue.hongshu.data.dao.EmployeeDao  deleteEmployee $com.xue.hongshu.data.dao.EmployeeDao  deleteEmployeeById $com.xue.hongshu.data.dao.EmployeeDao  getAllEmployees $com.xue.hongshu.data.dao.EmployeeDao  getEmployeeById $com.xue.hongshu.data.dao.EmployeeDao  getEmployeeCount $com.xue.hongshu.data.dao.EmployeeDao  getEmployeeCountByStatus $com.xue.hongshu.data.dao.EmployeeDao  getEmployeesByStatus $com.xue.hongshu.data.dao.EmployeeDao  getOverdueEmployees $com.xue.hongshu.data.dao.EmployeeDao  insertEmployee $com.xue.hongshu.data.dao.EmployeeDao  insertEmployees $com.xue.hongshu.data.dao.EmployeeDao  updateEmployee $com.xue.hongshu.data.dao.EmployeeDao  updateEmployeeError $com.xue.hongshu.data.dao.EmployeeDao  updateEmployeePostInfo $com.xue.hongshu.data.dao.EmployeeDao  updateEmployeeStatus $com.xue.hongshu.data.dao.EmployeeDao  AppDatabase com.xue.hongshu.data.database  Context com.xue.hongshu.data.database  
Converters com.xue.hongshu.data.database  Database com.xue.hongshu.data.database  Employee com.xue.hongshu.data.database  EmployeeDao com.xue.hongshu.data.database  EmployeeStatus com.xue.hongshu.data.database  Room com.xue.hongshu.data.database  RoomDatabase com.xue.hongshu.data.database  String com.xue.hongshu.data.database  
TypeConverter com.xue.hongshu.data.database  TypeConverters com.xue.hongshu.data.database  Volatile com.xue.hongshu.data.database  databaseBuilder com.xue.hongshu.data.database  java com.xue.hongshu.data.database  synchronized com.xue.hongshu.data.database  AppDatabase )com.xue.hongshu.data.database.AppDatabase  	Companion )com.xue.hongshu.data.database.AppDatabase  Context )com.xue.hongshu.data.database.AppDatabase  EmployeeDao )com.xue.hongshu.data.database.AppDatabase  INSTANCE )com.xue.hongshu.data.database.AppDatabase  Room )com.xue.hongshu.data.database.AppDatabase  Volatile )com.xue.hongshu.data.database.AppDatabase  databaseBuilder )com.xue.hongshu.data.database.AppDatabase  employeeDao )com.xue.hongshu.data.database.AppDatabase  getDatabase )com.xue.hongshu.data.database.AppDatabase  java )com.xue.hongshu.data.database.AppDatabase  synchronized )com.xue.hongshu.data.database.AppDatabase  AppDatabase 3com.xue.hongshu.data.database.AppDatabase.Companion  INSTANCE 3com.xue.hongshu.data.database.AppDatabase.Companion  Room 3com.xue.hongshu.data.database.AppDatabase.Companion  databaseBuilder 3com.xue.hongshu.data.database.AppDatabase.Companion  getDatabase 3com.xue.hongshu.data.database.AppDatabase.Companion  java 3com.xue.hongshu.data.database.AppDatabase.Companion  synchronized 3com.xue.hongshu.data.database.AppDatabase.Companion  EmployeeStatus (com.xue.hongshu.data.database.Converters  Boolean com.xue.hongshu.data.entity  Employee com.xue.hongshu.data.entity  EmployeeStatus com.xue.hongshu.data.entity  EmployeeWithStats com.xue.hongshu.data.entity  Entity com.xue.hongshu.data.entity  Int com.xue.hongshu.data.entity  Long com.xue.hongshu.data.entity  
PrimaryKey com.xue.hongshu.data.entity  String com.xue.hongshu.data.entity  System com.xue.hongshu.data.entity  errorMessage $com.xue.hongshu.data.entity.Employee  id $com.xue.hongshu.data.entity.Employee  lastPostTime $com.xue.hongshu.data.entity.Employee  
lastPostTitle $com.xue.hongshu.data.entity.Employee  name $com.xue.hongshu.data.entity.Employee  status $com.xue.hongshu.data.entity.Employee  	xhsUserId $com.xue.hongshu.data.entity.Employee  xhsUserName $com.xue.hongshu.data.entity.Employee  ACTIVE *com.xue.hongshu.data.entity.EmployeeStatus  CHECKING *com.xue.hongshu.data.entity.EmployeeStatus  ERROR *com.xue.hongshu.data.entity.EmployeeStatus  name *com.xue.hongshu.data.entity.EmployeeStatus  valueOf *com.xue.hongshu.data.entity.EmployeeStatus  Boolean -com.xue.hongshu.data.entity.EmployeeWithStats  	Companion -com.xue.hongshu.data.entity.EmployeeWithStats  Employee -com.xue.hongshu.data.entity.EmployeeWithStats  Int -com.xue.hongshu.data.entity.EmployeeWithStats  OVERDUE_DAYS -com.xue.hongshu.data.entity.EmployeeWithStats  daysSinceLastPost -com.xue.hongshu.data.entity.EmployeeWithStats  employee -com.xue.hongshu.data.entity.EmployeeWithStats  	isOverdue -com.xue.hongshu.data.entity.EmployeeWithStats  OVERDUE_DAYS 7com.xue.hongshu.data.entity.EmployeeWithStats.Companion  Calendar com.xue.hongshu.repository  Employee com.xue.hongshu.repository  EmployeeDao com.xue.hongshu.repository  EmployeeRepository com.xue.hongshu.repository  EmployeeStatus com.xue.hongshu.repository  EmployeeWithStats com.xue.hongshu.repository  	Exception com.xue.hongshu.repository  Flow com.xue.hongshu.repository  Int com.xue.hongshu.repository  List com.xue.hongshu.repository  Locale com.xue.hongshu.repository  Long com.xue.hongshu.repository  Regex com.xue.hongshu.repository  SimpleDateFormat com.xue.hongshu.repository  String com.xue.hongshu.repository  System com.xue.hongshu.repository  TimeUnit com.xue.hongshu.repository  XhsUserData com.xue.hongshu.repository  contains com.xue.hongshu.repository  isEmpty com.xue.hongshu.repository  listOf com.xue.hongshu.repository  map com.xue.hongshu.repository  matches com.xue.hongshu.repository  replace com.xue.hongshu.repository  toIntOrNull com.xue.hongshu.repository  Calendar -com.xue.hongshu.repository.EmployeeRepository  EmployeeStatus -com.xue.hongshu.repository.EmployeeRepository  EmployeeWithStats -com.xue.hongshu.repository.EmployeeRepository  Int -com.xue.hongshu.repository.EmployeeRepository  Locale -com.xue.hongshu.repository.EmployeeRepository  Regex -com.xue.hongshu.repository.EmployeeRepository  SimpleDateFormat -com.xue.hongshu.repository.EmployeeRepository  System -com.xue.hongshu.repository.EmployeeRepository  TimeUnit -com.xue.hongshu.repository.EmployeeRepository  addEmployee -com.xue.hongshu.repository.EmployeeRepository  calculateDaysSinceLastPost -com.xue.hongshu.repository.EmployeeRepository  contains -com.xue.hongshu.repository.EmployeeRepository  deleteEmployee -com.xue.hongshu.repository.EmployeeRepository  employeeDao -com.xue.hongshu.repository.EmployeeRepository  getEmployeeById -com.xue.hongshu.repository.EmployeeRepository  getEmployeeCount -com.xue.hongshu.repository.EmployeeRepository  getEmployeeCountByStatus -com.xue.hongshu.repository.EmployeeRepository  getEmployeesWithStats -com.xue.hongshu.repository.EmployeeRepository  isEmpty -com.xue.hongshu.repository.EmployeeRepository  listOf -com.xue.hongshu.repository.EmployeeRepository  map -com.xue.hongshu.repository.EmployeeRepository  matches -com.xue.hongshu.repository.EmployeeRepository  parseTimeString -com.xue.hongshu.repository.EmployeeRepository  replace -com.xue.hongshu.repository.EmployeeRepository  toIntOrNull -com.xue.hongshu.repository.EmployeeRepository  updateEmployeeError -com.xue.hongshu.repository.EmployeeRepository  updateEmployeeStatus -com.xue.hongshu.repository.EmployeeRepository  updateEmployeeWithXhsData -com.xue.hongshu.repository.EmployeeRepository  AddEmployeeDialog com.xue.hongshu.ui.component  AlertDialog com.xue.hongshu.ui.component  	Alignment com.xue.hongshu.ui.component  Arrangement com.xue.hongshu.ui.component  
AssistChip com.xue.hongshu.ui.component  AssistChipDefaults com.xue.hongshu.ui.component  Boolean com.xue.hongshu.ui.component  Button com.xue.hongshu.ui.component  Card com.xue.hongshu.ui.component  CardDefaults com.xue.hongshu.ui.component  CircularProgressIndicator com.xue.hongshu.ui.component  Color com.xue.hongshu.ui.component  Column com.xue.hongshu.ui.component  
Composable com.xue.hongshu.ui.component  
ConfigManager com.xue.hongshu.ui.component  ConnectionQuality com.xue.hongshu.ui.component  Date com.xue.hongshu.ui.component  
DetailSection com.xue.hongshu.ui.component  EmployeeCard com.xue.hongshu.ui.component  EmployeeStatus com.xue.hongshu.ui.component  EmployeeWithStats com.xue.hongshu.ui.component  ErrorHandler com.xue.hongshu.ui.component  
FontWeight com.xue.hongshu.ui.component  Icon com.xue.hongshu.ui.component  
IconButton com.xue.hongshu.ui.component  Icons com.xue.hongshu.ui.component  Int com.xue.hongshu.ui.component  KeyboardOptions com.xue.hongshu.ui.component  KeyboardType com.xue.hongshu.ui.component  
LazyColumn com.xue.hongshu.ui.component  List com.xue.hongshu.ui.component  Locale com.xue.hongshu.ui.component  LogStats com.xue.hongshu.ui.component  Long com.xue.hongshu.ui.component  Map com.xue.hongshu.ui.component  
MaterialTheme com.xue.hongshu.ui.component  Modifier com.xue.hongshu.ui.component  NetworkState com.xue.hongshu.ui.component  OutlinedButton com.xue.hongshu.ui.component  OutlinedTextField com.xue.hongshu.ui.component  Pair com.xue.hongshu.ui.component  Row com.xue.hongshu.ui.component  Spacer com.xue.hongshu.ui.component  
StatisticItem com.xue.hongshu.ui.component  
Statistics com.xue.hongshu.ui.component  StatisticsCard com.xue.hongshu.ui.component  
StatusChip com.xue.hongshu.ui.component  
StatusItem com.xue.hongshu.ui.component  String com.xue.hongshu.ui.component  SystemMonitorCard com.xue.hongshu.ui.component  Text com.xue.hongshu.ui.component  
TextButton com.xue.hongshu.ui.component  Unit com.xue.hongshu.ui.component  androidx com.xue.hongshu.ui.component  assistChipColors com.xue.hongshu.ui.component  
cardColors com.xue.hongshu.ui.component  
cardElevation com.xue.hongshu.ui.component  
component1 com.xue.hongshu.ui.component  
component2 com.xue.hongshu.ui.component  fillMaxWidth com.xue.hongshu.ui.component  forEach com.xue.hongshu.ui.component  
formatTime com.xue.hongshu.ui.component  getConnectionQualityText com.xue.hongshu.ui.component  getDetectionLevelColor com.xue.hongshu.ui.component  getDetectionLevelText com.xue.hongshu.ui.component  getErrorTypeText com.xue.hongshu.ui.component  getNetworkColor com.xue.hongshu.ui.component  getNetworkDisplayText com.xue.hongshu.ui.component  getStrategyModeText com.xue.hongshu.ui.component  getValue com.xue.hongshu.ui.component  height com.xue.hongshu.ui.component  heightIn com.xue.hongshu.ui.component  isBlank com.xue.hongshu.ui.component  
isNotEmpty com.xue.hongshu.ui.component  
isNullOrEmpty com.xue.hongshu.ui.component  listOf com.xue.hongshu.ui.component  map com.xue.hongshu.ui.component  mutableStateOf com.xue.hongshu.ui.component  padding com.xue.hongshu.ui.component  provideDelegate com.xue.hongshu.ui.component  remember com.xue.hongshu.ui.component  setValue com.xue.hongshu.ui.component  size com.xue.hongshu.ui.component  spacedBy com.xue.hongshu.ui.component  to com.xue.hongshu.ui.component  trim com.xue.hongshu.ui.component  weight com.xue.hongshu.ui.component  width com.xue.hongshu.ui.component  DetectionLevel *com.xue.hongshu.ui.component.ConfigManager  StrategyMode *com.xue.hongshu.ui.component.ConfigManager  	ErrorType )com.xue.hongshu.ui.component.ErrorHandler  compose %com.xue.hongshu.ui.component.androidx  ui -com.xue.hongshu.ui.component.androidx.compose  graphics 0com.xue.hongshu.ui.component.androidx.compose.ui  Color 9com.xue.hongshu.ui.component.androidx.compose.ui.graphics  AlertDialog com.xue.hongshu.ui.screen  	Alignment com.xue.hongshu.ui.screen  Arrangement com.xue.hongshu.ui.screen  Box com.xue.hongshu.ui.screen  Button com.xue.hongshu.ui.screen  Card com.xue.hongshu.ui.screen  CircularProgressIndicator com.xue.hongshu.ui.screen  Column com.xue.hongshu.ui.screen  
Composable com.xue.hongshu.ui.screen  DisposableEffect com.xue.hongshu.ui.screen  EmployeeCard com.xue.hongshu.ui.screen  ErrorHandler com.xue.hongshu.ui.screen  ExperimentalMaterial3Api com.xue.hongshu.ui.screen  
FilterChip com.xue.hongshu.ui.screen  FloatingActionButton com.xue.hongshu.ui.screen  
FontFamily com.xue.hongshu.ui.screen  
FontWeight com.xue.hongshu.ui.screen  Icon com.xue.hongshu.ui.screen  
IconButton com.xue.hongshu.ui.screen  Icons com.xue.hongshu.ui.screen  LaunchedEffect com.xue.hongshu.ui.screen  
LazyColumn com.xue.hongshu.ui.screen  LinearProgressIndicator com.xue.hongshu.ui.screen  LogFilterBar com.xue.hongshu.ui.screen  LogItem com.xue.hongshu.ui.screen  LogViewerScreen com.xue.hongshu.ui.screen  Logger com.xue.hongshu.ui.screen  
MainScreen com.xue.hongshu.ui.screen  
MainViewModel com.xue.hongshu.ui.screen  
MaterialTheme com.xue.hongshu.ui.screen  Modifier com.xue.hongshu.ui.screen  OptIn com.xue.hongshu.ui.screen  
PaddingValues com.xue.hongshu.ui.screen  Row com.xue.hongshu.ui.screen  Scaffold com.xue.hongshu.ui.screen  Spacer com.xue.hongshu.ui.screen  StatisticsCard com.xue.hongshu.ui.screen  String com.xue.hongshu.ui.screen  SystemMonitorCard com.xue.hongshu.ui.screen  Text com.xue.hongshu.ui.screen  
TextButton com.xue.hongshu.ui.screen  	TopAppBar com.xue.hongshu.ui.screen  Unit com.xue.hongshu.ui.screen  
WebViewTester com.xue.hongshu.ui.screen  androidx com.xue.hongshu.ui.screen  clearErrorStats com.xue.hongshu.ui.screen  	clearLogs com.xue.hongshu.ui.screen  com com.xue.hongshu.ui.screen  contains com.xue.hongshu.ui.screen  
exportLogs com.xue.hongshu.ui.screen  fillMaxSize com.xue.hongshu.ui.screen  fillMaxWidth com.xue.hongshu.ui.screen  filter com.xue.hongshu.ui.screen  first com.xue.hongshu.ui.screen  forEach com.xue.hongshu.ui.screen  
getErrorStats com.xue.hongshu.ui.screen  
getLogContent com.xue.hongshu.ui.screen  getLogStats com.xue.hongshu.ui.screen  getValue com.xue.hongshu.ui.screen  height com.xue.hongshu.ui.screen  isBlank com.xue.hongshu.ui.screen  isEmpty com.xue.hongshu.ui.screen  
isNotEmpty com.xue.hongshu.ui.screen  kotlinx com.xue.hongshu.ui.screen  launch com.xue.hongshu.ui.screen  let com.xue.hongshu.ui.screen  lines com.xue.hongshu.ui.screen  listOf com.xue.hongshu.ui.screen  mutableStateOf com.xue.hongshu.ui.screen  padding com.xue.hongshu.ui.screen  provideDelegate com.xue.hongshu.ui.screen  remember com.xue.hongshu.ui.screen  rememberCoroutineScope com.xue.hongshu.ui.screen  setValue com.xue.hongshu.ui.screen  spacedBy com.xue.hongshu.ui.screen  testBasicWebView com.xue.hongshu.ui.screen  testJavaScriptExecution com.xue.hongshu.ui.screen  
testXhsAccess com.xue.hongshu.ui.screen  to com.xue.hongshu.ui.screen  weight com.xue.hongshu.ui.screen  xue com.xue.hongshu.ui.screen.com  hongshu !com.xue.hongshu.ui.screen.com.xue  utils )com.xue.hongshu.ui.screen.com.xue.hongshu  LogStats /com.xue.hongshu.ui.screen.com.xue.hongshu.utils  Boolean com.xue.hongshu.ui.theme  Build com.xue.hongshu.ui.theme  
Composable com.xue.hongshu.ui.theme  DarkColorScheme com.xue.hongshu.ui.theme  
FontFamily com.xue.hongshu.ui.theme  
FontWeight com.xue.hongshu.ui.theme  HongshuTheme com.xue.hongshu.ui.theme  LightColorScheme com.xue.hongshu.ui.theme  Pink40 com.xue.hongshu.ui.theme  Pink80 com.xue.hongshu.ui.theme  Purple40 com.xue.hongshu.ui.theme  Purple80 com.xue.hongshu.ui.theme  PurpleGrey40 com.xue.hongshu.ui.theme  PurpleGrey80 com.xue.hongshu.ui.theme  
Typography com.xue.hongshu.ui.theme  Unit com.xue.hongshu.ui.theme  AntiDetectionHelper com.xue.hongshu.utils  Boolean com.xue.hongshu.utils  
ConfigManager com.xue.hongshu.utils  ConnectionQuality com.xue.hongshu.utils  ConnectivityManager com.xue.hongshu.utils  Context com.xue.hongshu.utils  CoroutineScope com.xue.hongshu.utils  Date com.xue.hongshu.utils  DetectionLevel com.xue.hongshu.utils  Dispatchers com.xue.hongshu.utils  ErrorHandler com.xue.hongshu.utils  	ErrorInfo com.xue.hongshu.utils  	ErrorType com.xue.hongshu.utils  	Exception com.xue.hongshu.utils  File com.xue.hongshu.utils  
FileWriter com.xue.hongshu.utils  Int com.xue.hongshu.utils  KEY_BASE_DELAY com.xue.hongshu.utils  KEY_CONSECUTIVE_FAILURES com.xue.hongshu.utils  KEY_DETECTION_LEVEL com.xue.hongshu.utils  KEY_LAST_SUCCESS_TIME com.xue.hongshu.utils  KEY_RETRY_COUNT com.xue.hongshu.utils  KEY_STRATEGY_MODE com.xue.hongshu.utils  KEY_USER_AGENT_INDEX com.xue.hongshu.utils  Locale com.xue.hongshu.utils  Log com.xue.hongshu.utils  LogStats com.xue.hongshu.utils  Logger com.xue.hongshu.utils  Long com.xue.hongshu.utils  MAX_LOG_SIZE com.xue.hongshu.utils  Map com.xue.hongshu.utils  MutableStateFlow com.xue.hongshu.utils  Network com.xue.hongshu.utils  NetworkCapabilities com.xue.hongshu.utils  NetworkMonitor com.xue.hongshu.utils  NetworkRequest com.xue.hongshu.utils  NetworkState com.xue.hongshu.utils  
PREFS_NAME com.xue.hongshu.utils  Random com.xue.hongshu.utils  Result com.xue.hongshu.utils  SharedPreferences com.xue.hongshu.utils  SimpleDateFormat com.xue.hongshu.utils  	StateFlow com.xue.hongshu.utils  StrategyMode com.xue.hongshu.utils  String com.xue.hongshu.utils  System com.xue.hongshu.utils  T com.xue.hongshu.utils  TAG com.xue.hongshu.utils  	Throwable com.xue.hongshu.utils  Unit com.xue.hongshu.utils  WebView com.xue.hongshu.utils  
WebViewTester com.xue.hongshu.utils  XhsWebViewClient com.xue.hongshu.utils  _connectionQuality com.xue.hongshu.utils  
_networkState com.xue.hongshu.utils  android com.xue.hongshu.utils  asStateFlow com.xue.hongshu.utils  buildString com.xue.hongshu.utils  cleanupLogFile com.xue.hongshu.utils  configureWebView com.xue.hongshu.utils  contains com.xue.hongshu.utils  copyTo com.xue.hongshu.utils  count com.xue.hongshu.utils  
dateFormat com.xue.hongshu.utils  delay com.xue.hongshu.utils  e com.xue.hongshu.utils  failure com.xue.hongshu.utils  
isNotEmpty com.xue.hongshu.utils  launch com.xue.hongshu.utils  let com.xue.hongshu.utils  lines com.xue.hongshu.utils  logFile com.xue.hongshu.utils  mutableMapOf com.xue.hongshu.utils  nextLong com.xue.hongshu.utils  readText com.xue.hongshu.utils  repeat com.xue.hongshu.utils  set com.xue.hongshu.utils  stackTraceToString com.xue.hongshu.utils  success com.xue.hongshu.utils  takeLast com.xue.hongshu.utils  toMap com.xue.hongshu.utils  
trimIndent com.xue.hongshu.utils  updateConnectionQuality com.xue.hongshu.utils  updateNetworkState com.xue.hongshu.utils  use com.xue.hongshu.utils  	writeText com.xue.hongshu.utils  Boolean #com.xue.hongshu.utils.ConfigManager  	Companion #com.xue.hongshu.utils.ConfigManager  Context #com.xue.hongshu.utils.ConfigManager  DetectionLevel #com.xue.hongshu.utils.ConfigManager  Int #com.xue.hongshu.utils.ConfigManager  KEY_BASE_DELAY #com.xue.hongshu.utils.ConfigManager  KEY_CONSECUTIVE_FAILURES #com.xue.hongshu.utils.ConfigManager  KEY_DETECTION_LEVEL #com.xue.hongshu.utils.ConfigManager  KEY_LAST_SUCCESS_TIME #com.xue.hongshu.utils.ConfigManager  KEY_RETRY_COUNT #com.xue.hongshu.utils.ConfigManager  KEY_STRATEGY_MODE #com.xue.hongshu.utils.ConfigManager  KEY_USER_AGENT_INDEX #com.xue.hongshu.utils.ConfigManager  Long #com.xue.hongshu.utils.ConfigManager  
PREFS_NAME #com.xue.hongshu.utils.ConfigManager  Random #com.xue.hongshu.utils.ConfigManager  SharedPreferences #com.xue.hongshu.utils.ConfigManager  StrategyMode #com.xue.hongshu.utils.ConfigManager  String #com.xue.hongshu.utils.ConfigManager  System #com.xue.hongshu.utils.ConfigManager  adjustDetectionLevel #com.xue.hongshu.utils.ConfigManager  getAdaptiveRetryCount #com.xue.hongshu.utils.ConfigManager  getBaseDelay #com.xue.hongshu.utils.ConfigManager  getConsecutiveFailures #com.xue.hongshu.utils.ConfigManager  getDetectionLevel #com.xue.hongshu.utils.ConfigManager  
getRetryCount #com.xue.hongshu.utils.ConfigManager  getStrategyMode #com.xue.hongshu.utils.ConfigManager  getUserAgentIndex #com.xue.hongshu.utils.ConfigManager  nextLong #com.xue.hongshu.utils.ConfigManager  prefs #com.xue.hongshu.utils.ConfigManager  resetConfig #com.xue.hongshu.utils.ConfigManager  setDetectionLevel #com.xue.hongshu.utils.ConfigManager  
trimIndent #com.xue.hongshu.utils.ConfigManager  Context -com.xue.hongshu.utils.ConfigManager.Companion  DetectionLevel -com.xue.hongshu.utils.ConfigManager.Companion  KEY_BASE_DELAY -com.xue.hongshu.utils.ConfigManager.Companion  KEY_CONSECUTIVE_FAILURES -com.xue.hongshu.utils.ConfigManager.Companion  KEY_DETECTION_LEVEL -com.xue.hongshu.utils.ConfigManager.Companion  KEY_LAST_SUCCESS_TIME -com.xue.hongshu.utils.ConfigManager.Companion  KEY_RETRY_COUNT -com.xue.hongshu.utils.ConfigManager.Companion  KEY_STRATEGY_MODE -com.xue.hongshu.utils.ConfigManager.Companion  KEY_USER_AGENT_INDEX -com.xue.hongshu.utils.ConfigManager.Companion  
PREFS_NAME -com.xue.hongshu.utils.ConfigManager.Companion  Random -com.xue.hongshu.utils.ConfigManager.Companion  StrategyMode -com.xue.hongshu.utils.ConfigManager.Companion  System -com.xue.hongshu.utils.ConfigManager.Companion  nextLong -com.xue.hongshu.utils.ConfigManager.Companion  
trimIndent -com.xue.hongshu.utils.ConfigManager.Companion  HIGH 2com.xue.hongshu.utils.ConfigManager.DetectionLevel  LOW 2com.xue.hongshu.utils.ConfigManager.DetectionLevel  MEDIUM 2com.xue.hongshu.utils.ConfigManager.DetectionLevel  name 2com.xue.hongshu.utils.ConfigManager.DetectionLevel  valueOf 2com.xue.hongshu.utils.ConfigManager.DetectionLevel  
AGGRESSIVE 0com.xue.hongshu.utils.ConfigManager.StrategyMode  NORMAL 0com.xue.hongshu.utils.ConfigManager.StrategyMode  STEALTH 0com.xue.hongshu.utils.ConfigManager.StrategyMode  name 0com.xue.hongshu.utils.ConfigManager.StrategyMode  valueOf 0com.xue.hongshu.utils.ConfigManager.StrategyMode  	EXCELLENT 'com.xue.hongshu.utils.ConnectionQuality  FAIR 'com.xue.hongshu.utils.ConnectionQuality  GOOD 'com.xue.hongshu.utils.ConnectionQuality  NONE 'com.xue.hongshu.utils.ConnectionQuality  POOR 'com.xue.hongshu.utils.ConnectionQuality  UNKNOWN 'com.xue.hongshu.utils.ConnectionQuality  NetworkCallback )com.xue.hongshu.utils.ConnectivityManager  Boolean "com.xue.hongshu.utils.ErrorHandler  	ErrorInfo "com.xue.hongshu.utils.ErrorHandler  	ErrorType "com.xue.hongshu.utils.ErrorHandler  	Exception "com.xue.hongshu.utils.ErrorHandler  Int "com.xue.hongshu.utils.ErrorHandler  Log "com.xue.hongshu.utils.ErrorHandler  Long "com.xue.hongshu.utils.ErrorHandler  Map "com.xue.hongshu.utils.ErrorHandler  Random "com.xue.hongshu.utils.ErrorHandler  Result "com.xue.hongshu.utils.ErrorHandler  String "com.xue.hongshu.utils.ErrorHandler  T "com.xue.hongshu.utils.ErrorHandler  TAG "com.xue.hongshu.utils.ErrorHandler  	Throwable "com.xue.hongshu.utils.ErrorHandler  Unit "com.xue.hongshu.utils.ErrorHandler  analyzeError "com.xue.hongshu.utils.ErrorHandler  calculateRetryDelay "com.xue.hongshu.utils.ErrorHandler  clearErrorStats "com.xue.hongshu.utils.ErrorHandler  contains "com.xue.hongshu.utils.ErrorHandler  delay "com.xue.hongshu.utils.ErrorHandler  
errorStats "com.xue.hongshu.utils.ErrorHandler  failure "com.xue.hongshu.utils.ErrorHandler  
getErrorStats "com.xue.hongshu.utils.ErrorHandler  getUserFriendlyMessage "com.xue.hongshu.utils.ErrorHandler  invoke "com.xue.hongshu.utils.ErrorHandler  mutableMapOf "com.xue.hongshu.utils.ErrorHandler  nextLong "com.xue.hongshu.utils.ErrorHandler  recordError "com.xue.hongshu.utils.ErrorHandler  repeat "com.xue.hongshu.utils.ErrorHandler  set "com.xue.hongshu.utils.ErrorHandler  success "com.xue.hongshu.utils.ErrorHandler  toMap "com.xue.hongshu.utils.ErrorHandler  copy ,com.xue.hongshu.utils.ErrorHandler.ErrorInfo  
maxRetries ,com.xue.hongshu.utils.ErrorHandler.ErrorInfo  message ,com.xue.hongshu.utils.ErrorHandler.ErrorInfo  
retryCount ,com.xue.hongshu.utils.ErrorHandler.ErrorInfo  	retryable ,com.xue.hongshu.utils.ErrorHandler.ErrorInfo  type ,com.xue.hongshu.utils.ErrorHandler.ErrorInfo  CERTIFICATE_ERROR ,com.xue.hongshu.utils.ErrorHandler.ErrorType  DETECTION_ERROR ,com.xue.hongshu.utils.ErrorHandler.ErrorType  
NETWORK_ERROR ,com.xue.hongshu.utils.ErrorHandler.ErrorType  PAGE_LOAD_ERROR ,com.xue.hongshu.utils.ErrorHandler.ErrorType  
PARSING_ERROR ,com.xue.hongshu.utils.ErrorHandler.ErrorType  	SSL_ERROR ,com.xue.hongshu.utils.ErrorHandler.ErrorType  
TIMEOUT_ERROR ,com.xue.hongshu.utils.ErrorHandler.ErrorType  
UNKNOWN_ERROR ,com.xue.hongshu.utils.ErrorHandler.ErrorType  
debugCount com.xue.hongshu.utils.LogStats  
errorCount com.xue.hongshu.utils.LogStats  fileSize com.xue.hongshu.utils.LogStats  getFormattedSize com.xue.hongshu.utils.LogStats  	infoCount com.xue.hongshu.utils.LogStats  
totalLines com.xue.hongshu.utils.LogStats  warningCount com.xue.hongshu.utils.LogStats  CoroutineScope com.xue.hongshu.utils.Logger  Date com.xue.hongshu.utils.Logger  Dispatchers com.xue.hongshu.utils.Logger  File com.xue.hongshu.utils.Logger  
FileWriter com.xue.hongshu.utils.Logger  
LOG_FILE_NAME com.xue.hongshu.utils.Logger  Locale com.xue.hongshu.utils.Logger  Log com.xue.hongshu.utils.Logger  LogStats com.xue.hongshu.utils.Logger  MAX_LOG_SIZE com.xue.hongshu.utils.Logger  SimpleDateFormat com.xue.hongshu.utils.Logger  System com.xue.hongshu.utils.Logger  TAG com.xue.hongshu.utils.Logger  buildString com.xue.hongshu.utils.Logger  cleanupLogFile com.xue.hongshu.utils.Logger  	clearLogs com.xue.hongshu.utils.Logger  contains com.xue.hongshu.utils.Logger  context com.xue.hongshu.utils.Logger  copyTo com.xue.hongshu.utils.Logger  count com.xue.hongshu.utils.Logger  d com.xue.hongshu.utils.Logger  
dateFormat com.xue.hongshu.utils.Logger  e com.xue.hongshu.utils.Logger  
exportLogs com.xue.hongshu.utils.Logger  
getLogContent com.xue.hongshu.utils.Logger  getLogStats com.xue.hongshu.utils.Logger  i com.xue.hongshu.utils.Logger  init com.xue.hongshu.utils.Logger  
isNotEmpty com.xue.hongshu.utils.Logger  launch com.xue.hongshu.utils.Logger  let com.xue.hongshu.utils.Logger  lines com.xue.hongshu.utils.Logger  logDataExtraction com.xue.hongshu.utils.Logger  logFile com.xue.hongshu.utils.Logger  
logUserAction com.xue.hongshu.utils.Logger  readText com.xue.hongshu.utils.Logger  stackTraceToString com.xue.hongshu.utils.Logger  takeLast com.xue.hongshu.utils.Logger  use com.xue.hongshu.utils.Logger  w com.xue.hongshu.utils.Logger  	writeText com.xue.hongshu.utils.Logger  writeToFile com.xue.hongshu.utils.Logger  Boolean $com.xue.hongshu.utils.NetworkMonitor  ConnectionQuality $com.xue.hongshu.utils.NetworkMonitor  ConnectivityManager $com.xue.hongshu.utils.NetworkMonitor  Context $com.xue.hongshu.utils.NetworkMonitor  Log $com.xue.hongshu.utils.NetworkMonitor  Long $com.xue.hongshu.utils.NetworkMonitor  MutableStateFlow $com.xue.hongshu.utils.NetworkMonitor  Network $com.xue.hongshu.utils.NetworkMonitor  NetworkCapabilities $com.xue.hongshu.utils.NetworkMonitor  NetworkRequest $com.xue.hongshu.utils.NetworkMonitor  NetworkState $com.xue.hongshu.utils.NetworkMonitor  	StateFlow $com.xue.hongshu.utils.NetworkMonitor  String $com.xue.hongshu.utils.NetworkMonitor  TAG $com.xue.hongshu.utils.NetworkMonitor  _connectionQuality $com.xue.hongshu.utils.NetworkMonitor  
_networkState $com.xue.hongshu.utils.NetworkMonitor  asStateFlow $com.xue.hongshu.utils.NetworkMonitor  connectionQuality $com.xue.hongshu.utils.NetworkMonitor  connectivityManager $com.xue.hongshu.utils.NetworkMonitor  context $com.xue.hongshu.utils.NetworkMonitor  isNetworkAvailable $com.xue.hongshu.utils.NetworkMonitor  networkCallback $com.xue.hongshu.utils.NetworkMonitor  networkState $com.xue.hongshu.utils.NetworkMonitor  startMonitoring $com.xue.hongshu.utils.NetworkMonitor  stopMonitoring $com.xue.hongshu.utils.NetworkMonitor  updateConnectionQuality $com.xue.hongshu.utils.NetworkMonitor  updateNetworkState $com.xue.hongshu.utils.NetworkMonitor  ConnectionQuality .com.xue.hongshu.utils.NetworkMonitor.Companion  Context .com.xue.hongshu.utils.NetworkMonitor.Companion  Log .com.xue.hongshu.utils.NetworkMonitor.Companion  MutableStateFlow .com.xue.hongshu.utils.NetworkMonitor.Companion  NetworkCapabilities .com.xue.hongshu.utils.NetworkMonitor.Companion  NetworkRequest .com.xue.hongshu.utils.NetworkMonitor.Companion  NetworkState .com.xue.hongshu.utils.NetworkMonitor.Companion  TAG .com.xue.hongshu.utils.NetworkMonitor.Companion  _connectionQuality .com.xue.hongshu.utils.NetworkMonitor.Companion  
_networkState .com.xue.hongshu.utils.NetworkMonitor.Companion  asStateFlow .com.xue.hongshu.utils.NetworkMonitor.Companion  updateConnectionQuality .com.xue.hongshu.utils.NetworkMonitor.Companion  updateNetworkState .com.xue.hongshu.utils.NetworkMonitor.Companion  NetworkCallback 8com.xue.hongshu.utils.NetworkMonitor.ConnectivityManager  CELLULAR "com.xue.hongshu.utils.NetworkState  DISCONNECTED "com.xue.hongshu.utils.NetworkState  ETHERNET "com.xue.hongshu.utils.NetworkState  OTHER "com.xue.hongshu.utils.NetworkState  UNKNOWN "com.xue.hongshu.utils.NetworkState  WIFI "com.xue.hongshu.utils.NetworkState  AntiDetectionHelper #com.xue.hongshu.utils.WebViewTester  Log #com.xue.hongshu.utils.WebViewTester  TAG #com.xue.hongshu.utils.WebViewTester  WebView #com.xue.hongshu.utils.WebViewTester  XhsWebViewClient #com.xue.hongshu.utils.WebViewTester  configureWebView #com.xue.hongshu.utils.WebViewTester  testBasicWebView #com.xue.hongshu.utils.WebViewTester  testJavaScriptExecution #com.xue.hongshu.utils.WebViewTester  
testXhsAccess #com.xue.hongshu.utils.WebViewTester  
trimIndent #com.xue.hongshu.utils.WebViewTester  webkit com.xue.hongshu.utils.android  WebResourceError $com.xue.hongshu.utils.android.webkit  WebResourceRequest $com.xue.hongshu.utils.android.webkit  
WebViewClient $com.xue.hongshu.utils.android.webkit  Boolean com.xue.hongshu.viewmodel  Class com.xue.hongshu.viewmodel  Employee com.xue.hongshu.viewmodel  EmployeeRepository com.xue.hongshu.viewmodel  EmployeeStatus com.xue.hongshu.viewmodel  EmployeeWithStats com.xue.hongshu.viewmodel  	Exception com.xue.hongshu.viewmodel  Float com.xue.hongshu.viewmodel  IllegalArgumentException com.xue.hongshu.viewmodel  Int com.xue.hongshu.viewmodel  List com.xue.hongshu.viewmodel  MainUiState com.xue.hongshu.viewmodel  
MainViewModel com.xue.hongshu.viewmodel  MainViewModelFactory com.xue.hongshu.viewmodel  MutableStateFlow com.xue.hongshu.viewmodel  SharingStarted com.xue.hongshu.viewmodel  	StateFlow com.xue.hongshu.viewmodel  
Statistics com.xue.hongshu.viewmodel  String com.xue.hongshu.viewmodel  Suppress com.xue.hongshu.viewmodel  T com.xue.hongshu.viewmodel  UUID com.xue.hongshu.viewmodel  	ViewModel com.xue.hongshu.viewmodel  ViewModelProvider com.xue.hongshu.viewmodel  WhileSubscribed com.xue.hongshu.viewmodel  XhsUserData com.xue.hongshu.viewmodel  _uiState com.xue.hongshu.viewmodel  asStateFlow com.xue.hongshu.viewmodel  	employees com.xue.hongshu.viewmodel  	emptyList com.xue.hongshu.viewmodel  forEachIndexed com.xue.hongshu.viewmodel  java com.xue.hongshu.viewmodel  kotlinx com.xue.hongshu.viewmodel  launch com.xue.hongshu.viewmodel  loadStatistics com.xue.hongshu.viewmodel  map com.xue.hongshu.viewmodel  
repository com.xue.hongshu.viewmodel  startCheckingEmployee com.xue.hongshu.viewmodel  stateIn com.xue.hongshu.viewmodel  batchCheckProgress %com.xue.hongshu.viewmodel.MainUiState  copy %com.xue.hongshu.viewmodel.MainUiState  currentCheckingEmployeeId %com.xue.hongshu.viewmodel.MainUiState  error %com.xue.hongshu.viewmodel.MainUiState  isBatchChecking %com.xue.hongshu.viewmodel.MainUiState  message %com.xue.hongshu.viewmodel.MainUiState  
statistics %com.xue.hongshu.viewmodel.MainUiState  Employee 'com.xue.hongshu.viewmodel.MainViewModel  EmployeeStatus 'com.xue.hongshu.viewmodel.MainViewModel  MainUiState 'com.xue.hongshu.viewmodel.MainViewModel  MutableStateFlow 'com.xue.hongshu.viewmodel.MainViewModel  SharingStarted 'com.xue.hongshu.viewmodel.MainViewModel  
Statistics 'com.xue.hongshu.viewmodel.MainViewModel  UUID 'com.xue.hongshu.viewmodel.MainViewModel  WhileSubscribed 'com.xue.hongshu.viewmodel.MainViewModel  
_employees 'com.xue.hongshu.viewmodel.MainViewModel  _uiState 'com.xue.hongshu.viewmodel.MainViewModel  addEmployee 'com.xue.hongshu.viewmodel.MainViewModel  asStateFlow 'com.xue.hongshu.viewmodel.MainViewModel  
clearError 'com.xue.hongshu.viewmodel.MainViewModel  clearMessage 'com.xue.hongshu.viewmodel.MainViewModel  deleteEmployee 'com.xue.hongshu.viewmodel.MainViewModel  	employees 'com.xue.hongshu.viewmodel.MainViewModel  	emptyList 'com.xue.hongshu.viewmodel.MainViewModel  forEachIndexed 'com.xue.hongshu.viewmodel.MainViewModel  kotlinx 'com.xue.hongshu.viewmodel.MainViewModel  launch 'com.xue.hongshu.viewmodel.MainViewModel  loadStatistics 'com.xue.hongshu.viewmodel.MainViewModel  map 'com.xue.hongshu.viewmodel.MainViewModel  
repository 'com.xue.hongshu.viewmodel.MainViewModel  startBatchCheck 'com.xue.hongshu.viewmodel.MainViewModel  startCheckingEmployee 'com.xue.hongshu.viewmodel.MainViewModel  stateIn 'com.xue.hongshu.viewmodel.MainViewModel  uiState 'com.xue.hongshu.viewmodel.MainViewModel  viewModelScope 'com.xue.hongshu.viewmodel.MainViewModel  IllegalArgumentException .com.xue.hongshu.viewmodel.MainViewModelFactory  
MainViewModel .com.xue.hongshu.viewmodel.MainViewModelFactory  java .com.xue.hongshu.viewmodel.MainViewModelFactory  
repository .com.xue.hongshu.viewmodel.MainViewModelFactory  activeEmployees $com.xue.hongshu.viewmodel.Statistics  errorEmployees $com.xue.hongshu.viewmodel.Statistics  overdueEmployees $com.xue.hongshu.viewmodel.Statistics  totalEmployees $com.xue.hongshu.viewmodel.Statistics  Factory +com.xue.hongshu.viewmodel.ViewModelProvider  AntiDetectionHelper com.xue.hongshu.webview  CoroutineScope com.xue.hongshu.webview  Dispatchers com.xue.hongshu.webview  ErrorHandler com.xue.hongshu.webview  	Exception com.xue.hongshu.webview  Log com.xue.hongshu.webview  Long com.xue.hongshu.webview  	MAX_DELAY com.xue.hongshu.webview  	MIN_DELAY com.xue.hongshu.webview  Random com.xue.hongshu.webview  Regex com.xue.hongshu.webview  SslError com.xue.hongshu.webview  SslErrorHandler com.xue.hongshu.webview  String com.xue.hongshu.webview  System com.xue.hongshu.webview  TAG com.xue.hongshu.webview  
TIMEOUT_DELAY com.xue.hongshu.webview  	Throwable com.xue.hongshu.webview  Unit com.xue.hongshu.webview  WebSettings com.xue.hongshu.webview  WebView com.xue.hongshu.webview  
WebViewClient com.xue.hongshu.webview  XhsUserData com.xue.hongshu.webview  XhsWebViewClient com.xue.hongshu.webview  analyzeError com.xue.hongshu.webview  android com.xue.hongshu.webview  calculateRetryDelay com.xue.hongshu.webview  contains com.xue.hongshu.webview  delay com.xue.hongshu.webview  extractUserData com.xue.hongshu.webview  getUserFriendlyMessage com.xue.hongshu.webview  handleError com.xue.hongshu.webview  isEmpty com.xue.hongshu.webview  isExtracting com.xue.hongshu.webview  kotlinx com.xue.hongshu.webview  launch com.xue.hongshu.webview  listOf com.xue.hongshu.webview  nextInt com.xue.hongshu.webview  nextLong com.xue.hongshu.webview  recordError com.xue.hongshu.webview  replace com.xue.hongshu.webview  
retryCount com.xue.hongshu.webview  trim com.xue.hongshu.webview  
trimIndent com.xue.hongshu.webview  Random +com.xue.hongshu.webview.AntiDetectionHelper  WebSettings +com.xue.hongshu.webview.AntiDetectionHelper  addRandomInteractions +com.xue.hongshu.webview.AntiDetectionHelper  bypassCommonDetection +com.xue.hongshu.webview.AntiDetectionHelper  configureWebView +com.xue.hongshu.webview.AntiDetectionHelper  getRandomDelay +com.xue.hongshu.webview.AntiDetectionHelper  getRandomUserAgent +com.xue.hongshu.webview.AntiDetectionHelper  injectAntiDetectionScript +com.xue.hongshu.webview.AntiDetectionHelper  listOf +com.xue.hongshu.webview.AntiDetectionHelper  nextInt +com.xue.hongshu.webview.AntiDetectionHelper  nextLong +com.xue.hongshu.webview.AntiDetectionHelper  simulateHumanScroll +com.xue.hongshu.webview.AntiDetectionHelper  simulateMouseMovement +com.xue.hongshu.webview.AntiDetectionHelper  
trimIndent +com.xue.hongshu.webview.AntiDetectionHelper  
userAgents +com.xue.hongshu.webview.AntiDetectionHelper  	ErrorInfo $com.xue.hongshu.webview.ErrorHandler  	ErrorType $com.xue.hongshu.webview.ErrorHandler  lastPostTime #com.xue.hongshu.webview.XhsUserData  
lastPostTitle #com.xue.hongshu.webview.XhsUserData  userName #com.xue.hongshu.webview.XhsUserData  CoroutineScope (com.xue.hongshu.webview.XhsWebViewClient  Dispatchers (com.xue.hongshu.webview.XhsWebViewClient  ErrorHandler (com.xue.hongshu.webview.XhsWebViewClient  	Exception (com.xue.hongshu.webview.XhsWebViewClient  Log (com.xue.hongshu.webview.XhsWebViewClient  	MAX_DELAY (com.xue.hongshu.webview.XhsWebViewClient  	MIN_DELAY (com.xue.hongshu.webview.XhsWebViewClient  Random (com.xue.hongshu.webview.XhsWebViewClient  Regex (com.xue.hongshu.webview.XhsWebViewClient  SslError (com.xue.hongshu.webview.XhsWebViewClient  SslErrorHandler (com.xue.hongshu.webview.XhsWebViewClient  String (com.xue.hongshu.webview.XhsWebViewClient  System (com.xue.hongshu.webview.XhsWebViewClient  TAG (com.xue.hongshu.webview.XhsWebViewClient  
TIMEOUT_DELAY (com.xue.hongshu.webview.XhsWebViewClient  	Throwable (com.xue.hongshu.webview.XhsWebViewClient  Unit (com.xue.hongshu.webview.XhsWebViewClient  WebView (com.xue.hongshu.webview.XhsWebViewClient  XhsUserData (com.xue.hongshu.webview.XhsWebViewClient  analyzeError (com.xue.hongshu.webview.XhsWebViewClient  android (com.xue.hongshu.webview.XhsWebViewClient  calculateRetryDelay (com.xue.hongshu.webview.XhsWebViewClient  cancelTimeoutCheck (com.xue.hongshu.webview.XhsWebViewClient  contains (com.xue.hongshu.webview.XhsWebViewClient  delay (com.xue.hongshu.webview.XhsWebViewClient  extractUserData (com.xue.hongshu.webview.XhsWebViewClient  getDataExtractionScript (com.xue.hongshu.webview.XhsWebViewClient  getUserFriendlyMessage (com.xue.hongshu.webview.XhsWebViewClient  handleError (com.xue.hongshu.webview.XhsWebViewClient  invoke (com.xue.hongshu.webview.XhsWebViewClient  isEmpty (com.xue.hongshu.webview.XhsWebViewClient  isExtracting (com.xue.hongshu.webview.XhsWebViewClient  kotlinx (com.xue.hongshu.webview.XhsWebViewClient  launch (com.xue.hongshu.webview.XhsWebViewClient  
maxRetries (com.xue.hongshu.webview.XhsWebViewClient  nextLong (com.xue.hongshu.webview.XhsWebViewClient  onDataExtracted (com.xue.hongshu.webview.XhsWebViewClient  onError (com.xue.hongshu.webview.XhsWebViewClient  onRetry (com.xue.hongshu.webview.XhsWebViewClient  parseAndHandleResult (com.xue.hongshu.webview.XhsWebViewClient  parseUserDataFromJson (com.xue.hongshu.webview.XhsWebViewClient  recordError (com.xue.hongshu.webview.XhsWebViewClient  replace (com.xue.hongshu.webview.XhsWebViewClient  
retryCount (com.xue.hongshu.webview.XhsWebViewClient  startTimeoutCheck (com.xue.hongshu.webview.XhsWebViewClient  
timeoutJob (com.xue.hongshu.webview.XhsWebViewClient  trim (com.xue.hongshu.webview.XhsWebViewClient  
trimIndent (com.xue.hongshu.webview.XhsWebViewClient  CoroutineScope 2com.xue.hongshu.webview.XhsWebViewClient.Companion  Dispatchers 2com.xue.hongshu.webview.XhsWebViewClient.Companion  ErrorHandler 2com.xue.hongshu.webview.XhsWebViewClient.Companion  Log 2com.xue.hongshu.webview.XhsWebViewClient.Companion  	MAX_DELAY 2com.xue.hongshu.webview.XhsWebViewClient.Companion  	MIN_DELAY 2com.xue.hongshu.webview.XhsWebViewClient.Companion  Random 2com.xue.hongshu.webview.XhsWebViewClient.Companion  Regex 2com.xue.hongshu.webview.XhsWebViewClient.Companion  SslError 2com.xue.hongshu.webview.XhsWebViewClient.Companion  System 2com.xue.hongshu.webview.XhsWebViewClient.Companion  TAG 2com.xue.hongshu.webview.XhsWebViewClient.Companion  
TIMEOUT_DELAY 2com.xue.hongshu.webview.XhsWebViewClient.Companion  XhsUserData 2com.xue.hongshu.webview.XhsWebViewClient.Companion  analyzeError 2com.xue.hongshu.webview.XhsWebViewClient.Companion  android 2com.xue.hongshu.webview.XhsWebViewClient.Companion  calculateRetryDelay 2com.xue.hongshu.webview.XhsWebViewClient.Companion  contains 2com.xue.hongshu.webview.XhsWebViewClient.Companion  delay 2com.xue.hongshu.webview.XhsWebViewClient.Companion  extractUserData 2com.xue.hongshu.webview.XhsWebViewClient.Companion  getUserFriendlyMessage 2com.xue.hongshu.webview.XhsWebViewClient.Companion  handleError 2com.xue.hongshu.webview.XhsWebViewClient.Companion  invoke 2com.xue.hongshu.webview.XhsWebViewClient.Companion  isEmpty 2com.xue.hongshu.webview.XhsWebViewClient.Companion  isExtracting 2com.xue.hongshu.webview.XhsWebViewClient.Companion  kotlinx 2com.xue.hongshu.webview.XhsWebViewClient.Companion  launch 2com.xue.hongshu.webview.XhsWebViewClient.Companion  nextLong 2com.xue.hongshu.webview.XhsWebViewClient.Companion  recordError 2com.xue.hongshu.webview.XhsWebViewClient.Companion  replace 2com.xue.hongshu.webview.XhsWebViewClient.Companion  
retryCount 2com.xue.hongshu.webview.XhsWebViewClient.Companion  trim 2com.xue.hongshu.webview.XhsWebViewClient.Companion  
trimIndent 2com.xue.hongshu.webview.XhsWebViewClient.Companion  	ErrorInfo 5com.xue.hongshu.webview.XhsWebViewClient.ErrorHandler  	ErrorType 5com.xue.hongshu.webview.XhsWebViewClient.ErrorHandler  graphics 0com.xue.hongshu.webview.XhsWebViewClient.android  webkit 0com.xue.hongshu.webview.XhsWebViewClient.android  Bitmap 9com.xue.hongshu.webview.XhsWebViewClient.android.graphics  WebResourceError 7com.xue.hongshu.webview.XhsWebViewClient.android.webkit  WebResourceRequest 7com.xue.hongshu.webview.XhsWebViewClient.android.webkit  
coroutines 0com.xue.hongshu.webview.XhsWebViewClient.kotlinx  Job ;com.xue.hongshu.webview.XhsWebViewClient.kotlinx.coroutines  graphics com.xue.hongshu.webview.android  webkit com.xue.hongshu.webview.android  Bitmap (com.xue.hongshu.webview.android.graphics  WebResourceError &com.xue.hongshu.webview.android.webkit  WebResourceRequest &com.xue.hongshu.webview.android.webkit  
coroutines com.xue.hongshu.webview.kotlinx  Job *com.xue.hongshu.webview.kotlinx.coroutines  File java.io  
FileWriter java.io  copyTo java.io.File  delete java.io.File  exists java.io.File  length java.io.File  readText java.io.File  	writeText java.io.File  flush java.io.FileWriter  use java.io.FileWriter  write java.io.FileWriter  flush java.io.OutputStreamWriter  write java.io.OutputStreamWriter  write java.io.Writer  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  Runnable 	java.lang  
StringBuilder 	java.lang  isAssignableFrom java.lang.Class  invoke java.lang.Exception  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  append java.lang.StringBuilder  let java.lang.StringBuilder  stackTraceToString java.lang.StringBuilder  currentTimeMillis java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  parse java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  parse java.text.SimpleDateFormat  AlertDialog 	java.util  	Alignment 	java.util  Arrangement 	java.util  
AssistChip 	java.util  AssistChipDefaults 	java.util  Boolean 	java.util  Button 	java.util  Calendar 	java.util  Card 	java.util  CardDefaults 	java.util  CircularProgressIndicator 	java.util  Column 	java.util  
Composable 	java.util  Context 	java.util  CoroutineScope 	java.util  Date 	java.util  Dispatchers 	java.util  Employee 	java.util  EmployeeDao 	java.util  EmployeeRepository 	java.util  EmployeeStatus 	java.util  EmployeeWithStats 	java.util  ErrorHandler 	java.util  	Exception 	java.util  File 	java.util  
FileWriter 	java.util  Float 	java.util  Flow 	java.util  
FontWeight 	java.util  Icon 	java.util  
IconButton 	java.util  Icons 	java.util  Int 	java.util  List 	java.util  Locale 	java.util  Log 	java.util  LogStats 	java.util  Long 	java.util  MAX_LOG_SIZE 	java.util  MainUiState 	java.util  
MaterialTheme 	java.util  Modifier 	java.util  MutableStateFlow 	java.util  Regex 	java.util  Row 	java.util  SharingStarted 	java.util  SimpleDateFormat 	java.util  Spacer 	java.util  	StateFlow 	java.util  
Statistics 	java.util  
StatusChip 	java.util  String 	java.util  System 	java.util  TAG 	java.util  Text 	java.util  
TextButton 	java.util  	Throwable 	java.util  TimeUnit 	java.util  UUID 	java.util  Unit 	java.util  	ViewModel 	java.util  WhileSubscribed 	java.util  XhsUserData 	java.util  _uiState 	java.util  asStateFlow 	java.util  assistChipColors 	java.util  buildString 	java.util  
cardElevation 	java.util  cleanupLogFile 	java.util  contains 	java.util  copyTo 	java.util  count 	java.util  
dateFormat 	java.util  e 	java.util  	employees 	java.util  	emptyList 	java.util  fillMaxWidth 	java.util  forEachIndexed 	java.util  
formatTime 	java.util  getValue 	java.util  height 	java.util  isEmpty 	java.util  
isNotEmpty 	java.util  
isNullOrEmpty 	java.util  kotlinx 	java.util  launch 	java.util  let 	java.util  lines 	java.util  listOf 	java.util  loadStatistics 	java.util  logFile 	java.util  map 	java.util  matches 	java.util  mutableStateOf 	java.util  padding 	java.util  provideDelegate 	java.util  readText 	java.util  remember 	java.util  replace 	java.util  
repository 	java.util  setValue 	java.util  size 	java.util  stackTraceToString 	java.util  startCheckingEmployee 	java.util  stateIn 	java.util  takeLast 	java.util  to 	java.util  toIntOrNull 	java.util  use 	java.util  weight 	java.util  width 	java.util  	writeText 	java.util  YEAR java.util.Calendar  get java.util.Calendar  getInstance java.util.Calendar  time java.util.Date  	ErrorType java.util.ErrorHandler  
getDefault java.util.Locale  
randomUUID java.util.UUID  toString java.util.UUID  TimeUnit java.util.concurrent  DAYS java.util.concurrent.TimeUnit  HOURS java.util.concurrent.TimeUnit  MILLISECONDS java.util.concurrent.TimeUnit  MINUTES java.util.concurrent.TimeUnit  toDays java.util.concurrent.TimeUnit  toMillis java.util.concurrent.TimeUnit  Array kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  Int kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  Suppress kotlin  	Throwable kotlin  apply kotlin  let kotlin  map kotlin  repeat kotlin  stackTraceToString kotlin  synchronized kotlin  to kotlin  use kotlin  not kotlin.Boolean  isEmpty kotlin.CharSequence  sp 
kotlin.Double  times 
kotlin.Double  toLong 
kotlin.Double  div kotlin.Float  times kotlin.Float  toInt kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  invoke kotlin.Function3  	Companion 
kotlin.Int  	MAX_VALUE 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rem 
kotlin.Int  shl 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  toLong 
kotlin.Int  toString 
kotlin.Int  	MAX_VALUE kotlin.Int.Companion  	compareTo kotlin.Long  div kotlin.Long  invoke kotlin.Long  minus kotlin.Long  plus kotlin.Long  times kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.Result  failure 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  contains 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  lines 
kotlin.String  matches 
kotlin.String  replace 
kotlin.String  takeLast 
kotlin.String  to 
kotlin.String  toIntOrNull 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  let kotlin.Throwable  message kotlin.Throwable  stackTraceToString kotlin.Throwable  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  
MutableMap kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  first kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  mutableMapOf kotlin.collections  set kotlin.collections  takeLast kotlin.collections  toMap kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  count kotlin.collections.List  filter kotlin.collections.List  first kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  Entry kotlin.collections.Map  
isNotEmpty kotlin.collections.Map  map kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  clear kotlin.collections.MutableMap  getOrDefault kotlin.collections.MutableMap  set kotlin.collections.MutableMap  toMap kotlin.collections.MutableMap  SuspendFunction0 kotlin.coroutines  SuspendFunction1 kotlin.coroutines  invoke "kotlin.coroutines.SuspendFunction0  copyTo 	kotlin.io  readText 	kotlin.io  use 	kotlin.io  	writeText 	kotlin.io  Volatile 
kotlin.jvm  java 
kotlin.jvm  Random 
kotlin.random  Default kotlin.random.Random  nextInt kotlin.random.Random  nextLong kotlin.random.Random  nextInt kotlin.random.Random.Default  nextLong kotlin.random.Random.Default  contains 
kotlin.ranges  first 
kotlin.ranges  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  filter kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  map kotlin.sequences  MatchResult kotlin.text  Regex kotlin.text  buildString kotlin.text  contains kotlin.text  count kotlin.text  filter kotlin.text  first kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  lines kotlin.text  map kotlin.text  matches kotlin.text  repeat kotlin.text  replace kotlin.text  set kotlin.text  takeLast kotlin.text  toIntOrNull kotlin.text  trim kotlin.text  
trimIndent kotlin.text  groupValues kotlin.text.MatchResult  find kotlin.text.Regex  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  Date !kotlinx.coroutines.CoroutineScope  Employee !kotlinx.coroutines.CoroutineScope  EmployeeStatus !kotlinx.coroutines.CoroutineScope  ErrorHandler !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  
FileWriter !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  Logger !kotlinx.coroutines.CoroutineScope  MAX_LOG_SIZE !kotlinx.coroutines.CoroutineScope  
Statistics !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  
TIMEOUT_DELAY !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  UUID !kotlinx.coroutines.CoroutineScope  XhsWebViewClient !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  buildString !kotlinx.coroutines.CoroutineScope  calculateRetryDelay !kotlinx.coroutines.CoroutineScope  cleanupLogFile !kotlinx.coroutines.CoroutineScope  com !kotlinx.coroutines.CoroutineScope  copyTo !kotlinx.coroutines.CoroutineScope  createIntent !kotlinx.coroutines.CoroutineScope  
dateFormat !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  e !kotlinx.coroutines.CoroutineScope  
employeeId !kotlinx.coroutines.CoroutineScope  	employees !kotlinx.coroutines.CoroutineScope  extractUserData !kotlinx.coroutines.CoroutineScope  finish !kotlinx.coroutines.CoroutineScope  forEachIndexed !kotlinx.coroutines.CoroutineScope  
getErrorStats !kotlinx.coroutines.CoroutineScope  
getLogContent !kotlinx.coroutines.CoroutineScope  getLogStats !kotlinx.coroutines.CoroutineScope  handleError !kotlinx.coroutines.CoroutineScope  invoke !kotlinx.coroutines.CoroutineScope  isExtracting !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  loadStatistics !kotlinx.coroutines.CoroutineScope  logDataExtraction !kotlinx.coroutines.CoroutineScope  logFile !kotlinx.coroutines.CoroutineScope  
logUserAction !kotlinx.coroutines.CoroutineScope  map !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  
retryCount !kotlinx.coroutines.CoroutineScope  stackTraceToString !kotlinx.coroutines.CoroutineScope  
startActivity !kotlinx.coroutines.CoroutineScope  startCheckingEmployee !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  w !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  Boolean kotlinx.coroutines.flow  Employee kotlinx.coroutines.flow  EmployeeRepository kotlinx.coroutines.flow  EmployeeStatus kotlinx.coroutines.flow  EmployeeWithStats kotlinx.coroutines.flow  	Exception kotlinx.coroutines.flow  Float kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  Int kotlinx.coroutines.flow  List kotlinx.coroutines.flow  MainUiState kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  
Statistics kotlinx.coroutines.flow  String kotlinx.coroutines.flow  UUID kotlinx.coroutines.flow  	ViewModel kotlinx.coroutines.flow  WhileSubscribed kotlinx.coroutines.flow  XhsUserData kotlinx.coroutines.flow  _uiState kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	employees kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  forEachIndexed kotlinx.coroutines.flow  kotlinx kotlinx.coroutines.flow  launch kotlinx.coroutines.flow  loadStatistics kotlinx.coroutines.flow  map kotlinx.coroutines.flow  
repository kotlinx.coroutines.flow  startCheckingEmployee kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  map kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  	Companion &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  collectAsStateWithLifecycle !kotlinx.coroutines.flow.StateFlow  value !kotlinx.coroutines.flow.StateFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            