<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 基础配置：禁用明文传输，强制HTTPS -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- 信任系统证书 -->
            <certificates src="system"/>
            <!-- 仅在调试模式下信任用户证书 -->
            <certificates src="user"/>
        </trust-anchors>
    </base-config>

    <!-- 小红书域名安全配置 -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">xiaohongshu.com</domain>
        <domain includeSubdomains="true">www.xiaohongshu.com</domain>
        <domain includeSubdomains="true">fe-video-qc.xhscdn.com</domain>
        <domain includeSubdomains="true">sns-avatar-qc.xhscdn.com</domain>
        <domain includeSubdomains="true">ci.xiaohongshu.com</domain>
        <domain includeSubdomains="true">edith.xiaohongshu.com</domain>
        <domain includeSubdomains="true">picasso.xiaohongshu.com</domain>

        <!-- 证书固定配置 -->
        <pin-set expiration="2025-12-31">
            <!-- 小红书主域名证书指纹 (示例，需要获取实际证书) -->
            <pin digest="SHA-256">AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</pin>
            <!-- 备用证书指纹 -->
            <pin digest="SHA-256">BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=</pin>
        </pin-set>

        <trust-anchors>
            <certificates src="system"/>
            <!-- 生产环境移除用户证书信任 -->
            <certificates src="user"/>
        </trust-anchors>
    </domain-config>

    <!-- 本地开发服务器配置（仅调试模式） -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
    </domain-config>

    <!-- 调试配置 - 生产环境必须移除 -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>
