package com.xue.hongshu.webview

import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.SslErrorHandler
import android.net.http.SslError
import android.util.Log
import com.xue.hongshu.utils.ErrorHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random

class XhsWebViewClient(
    private val onDataExtracted: (XhsUserData) -> Unit,
    private val onError: (String) -> Unit,
    private val onRetry: (ErrorHandler.ErrorInfo) -> Unit = {}
) : WebViewClient() {
    
    companion object {
        private const val TAG = "XhsWebViewClient"
        private const val MIN_DELAY = 2000L // 最小延迟2秒
        private const val MAX_DELAY = 5000L // 最大延迟5秒
        private const val TIMEOUT_DELAY = 30000L // 30秒超时
    }
    
    private var isExtracting = false
    private var retryCount = 0
    private val maxRetries = 3
    private var timeoutJob: kotlinx.coroutines.Job? = null
    
    override fun onPageFinished(view: WebView?, url: String?) {
        super.onPageFinished(view, url)

        if (view == null) {
            Log.e(TAG, "WebView is null in onPageFinished")
            onError("WebView初始化失败")
            return
        }

        if (isExtracting) {
            Log.d(TAG, "Already extracting, skipping")
            return
        }

        Log.d(TAG, "Page finished loading: $url")

        // 检查URL是否是小红书页面
        if (url?.contains("xiaohongshu.com") != true) {
            Log.w(TAG, "Not a xiaohongshu page: $url")
            onError("页面加载错误：不是小红书页面")
            return
        }

        // 启动超时检查
        startTimeoutCheck()

        // 随机延迟，模拟真实用户行为
        val delay = Random.nextLong(MIN_DELAY, MAX_DELAY)
        Log.d(TAG, "Waiting ${delay}ms before extracting data")

        CoroutineScope(Dispatchers.Main).launch {
            delay(delay)
            extractUserData(view)
        }
    }

    override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
        super.onPageStarted(view, url, favicon)
        Log.d(TAG, "Page started loading: $url")
    }

    override fun onReceivedError(view: WebView?, request: android.webkit.WebResourceRequest?, error: android.webkit.WebResourceError?) {
        super.onReceivedError(view, request, error)
        Log.e(TAG, "WebView error: ${error?.description} for ${request?.url}")

        // 分析错误类型并提供更友好的错误信息
        val errorMessage = when (error?.errorCode) {
            android.webkit.WebViewClient.ERROR_HOST_LOOKUP -> "网络连接失败，请检查网络设置"
            android.webkit.WebViewClient.ERROR_CONNECT -> "无法连接到服务器"
            android.webkit.WebViewClient.ERROR_TIMEOUT -> "连接超时，请稍后重试"
            android.webkit.WebViewClient.ERROR_FAILED_SSL_HANDSHAKE -> "SSL连接失败"
            android.webkit.WebViewClient.ERROR_BAD_URL -> "URL格式错误"
            else -> "页面加载失败: ${error?.description}"
        }

        onError(errorMessage)
    }

    override fun onReceivedSslError(view: WebView?, handler: SslErrorHandler?, error: SslError?) {
        Log.w(TAG, "SSL Error: ${error?.toString()}")

        // 分析SSL错误类型和严重程度
        val sslErrorInfo = analyzeSslError(error)
        Log.w(TAG, "SSL Error details: ${sslErrorInfo.message} for URL: ${error?.url}")

        // 记录SSL错误统计
        recordSslError(error)

        val url = error?.url ?: ""
        val isXhsDomain = isXiaohongshuDomain(url)

        // 根据错误类型和域名决定处理策略
        when {
            // 对于小红书域名的轻微SSL错误，可以继续
            isXhsDomain && sslErrorInfo.canProceed -> {
                Log.w(TAG, "Proceeding with minor SSL error for XHS domain: $url")
                handler?.proceed()
            }

            // 对于严重的SSL错误，即使是小红书域名也要取消
            sslErrorInfo.isCritical -> {
                Log.e(TAG, "Critical SSL error, canceling connection: $url")
                handler?.cancel()
                onError("严重SSL安全错误: ${sslErrorInfo.message}")
            }

            // 对于非小红书域名，取消连接
            !isXhsDomain -> {
                Log.e(TAG, "SSL error for non-XHS domain, canceling: $url")
                handler?.cancel()
                onError("SSL连接失败: ${sslErrorInfo.message}")
            }

            // 其他情况，尝试重试
            else -> {
                Log.w(TAG, "SSL error, attempting retry: $url")
                if (canRetryOnSslError()) {
                    handler?.proceed()
                } else {
                    handler?.cancel()
                    onError("SSL连接失败: ${sslErrorInfo.message}")
                }
            }
        }
    }

    /**
     * 分析SSL错误详情
     */
    private fun analyzeSslError(error: SslError?): SslErrorInfo {
        if (error == null) {
            return SslErrorInfo("未知SSL错误", false, true)
        }

        return when (error.primaryError) {
            SslError.SSL_UNTRUSTED -> SslErrorInfo(
                "SSL证书不受信任 - 证书颁发机构未被认可",
                canProceed = false,
                isCritical = true
            )
            SslError.SSL_EXPIRED -> SslErrorInfo(
                "SSL证书已过期 - 证书有效期已结束",
                canProceed = false,
                isCritical = true
            )
            SslError.SSL_IDMISMATCH -> SslErrorInfo(
                "SSL证书域名不匹配 - 证书与访问域名不符",
                canProceed = true,  // 域名不匹配可能是CDN导致，可以尝试继续
                isCritical = false
            )
            SslError.SSL_NOTYETVALID -> SslErrorInfo(
                "SSL证书尚未生效 - 证书有效期未开始",
                canProceed = false,
                isCritical = true
            )
            SslError.SSL_DATE_INVALID -> SslErrorInfo(
                "SSL证书日期无效 - 系统时间可能不正确",
                canProceed = true,  // 可能是设备时间问题
                isCritical = false
            )
            SslError.SSL_INVALID -> SslErrorInfo(
                "SSL证书无效 - 证书格式或内容错误",
                canProceed = false,
                isCritical = true
            )
            else -> SslErrorInfo(
                "未知SSL错误 - 错误代码: ${error.primaryError}",
                canProceed = false,
                isCritical = true
            )
        }
    }

    /**
     * 检查是否为小红书域名
     */
    private fun isXiaohongshuDomain(url: String): Boolean {
        val xhsDomains = listOf(
            "xiaohongshu.com",
            "xhscdn.com",
            "fe-video-qc.xhscdn.com",
            "sns-avatar-qc.xhscdn.com",
            "ci.xiaohongshu.com",
            "edith.xiaohongshu.com",
            "picasso.xiaohongshu.com"
        )

        return xhsDomains.any { domain ->
            url.contains(domain, ignoreCase = true)
        }
    }

    /**
     * 记录SSL错误统计
     */
    private fun recordSslError(error: SslError?) {
        error?.let {
            val errorType = when (it.primaryError) {
                SslError.SSL_UNTRUSTED -> "UNTRUSTED"
                SslError.SSL_EXPIRED -> "EXPIRED"
                SslError.SSL_IDMISMATCH -> "ID_MISMATCH"
                SslError.SSL_NOTYETVALID -> "NOT_YET_VALID"
                SslError.SSL_DATE_INVALID -> "DATE_INVALID"
                SslError.SSL_INVALID -> "INVALID"
                else -> "UNKNOWN"
            }

            Log.i(TAG, "SSL_ERROR_STATS: type=$errorType, url=${it.url}, certificate=${it.certificate?.issuedBy?.dName}")
        }
    }

    /**
     * 判断是否可以在SSL错误时重试
     */
    private fun canRetryOnSslError(): Boolean {
        // 简单的重试逻辑，可以根据需要扩展
        return retryCount < maxRetries
    }

    /**
     * SSL错误信息数据类
     */
    private data class SslErrorInfo(
        val message: String,
        val canProceed: Boolean,
        val isCritical: Boolean
    )
    
    private fun extractUserData(webView: WebView) {
        if (isExtracting) {
            Log.d(TAG, "Already extracting, returning")
            return
        }

        isExtracting = true
        Log.d(TAG, "Starting data extraction")

        val script = getDataExtractionScript()
        Log.d(TAG, "Executing JavaScript for data extraction")

        webView.evaluateJavascript(script) { result ->
            try {
                Log.d(TAG, "JavaScript execution completed")
                Log.d(TAG, "JavaScript result: $result")

                if (result == null) {
                    Log.e(TAG, "JavaScript result is null")
                    handleError("JavaScript执行失败", ErrorHandler.ErrorType.PARSING_ERROR)
                    return@evaluateJavascript
                }

                parseAndHandleResult(result)
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing JavaScript result", e)
                handleError("数据解析失败: ${e.message}", ErrorHandler.ErrorType.PARSING_ERROR, e)
            } finally {
                isExtracting = false
                cancelTimeoutCheck()
            }
        }
    }

    private fun startTimeoutCheck() {
        cancelTimeoutCheck()
        timeoutJob = CoroutineScope(Dispatchers.Main).launch {
            kotlinx.coroutines.delay(TIMEOUT_DELAY)
            if (isExtracting) {
                Log.w(TAG, "Data extraction timeout")
                handleError("检查超时，请重试", ErrorHandler.ErrorType.TIMEOUT_ERROR)
            }
        }
    }

    private fun cancelTimeoutCheck() {
        timeoutJob?.cancel()
        timeoutJob = null
    }
    
    private fun parseAndHandleResult(result: String) {
        if (result == "null" || result.isEmpty()) {
            handleError("未找到用户数据", ErrorHandler.ErrorType.PARSING_ERROR)
            return
        }

        try {
            // 移除JavaScript返回的引号
            val cleanResult = result.trim('"').replace("\\\"", "\"")

            // 简单的JSON解析（实际项目中建议使用Gson）
            if (cleanResult.contains("\"success\":true")) {
                val userData = parseUserDataFromJson(cleanResult)
                retryCount = 0 // 成功后重置重试计数
                onDataExtracted(userData)
            } else {
                val errorMatch = Regex("\"error\":\"([^\"]+)\"").find(cleanResult)
                val errorMessage = errorMatch?.groupValues?.get(1) ?: "未知错误"

                // 分析错误类型
                val errorType = when {
                    errorMessage.contains("页面结构") -> ErrorHandler.ErrorType.PAGE_LOAD_ERROR
                    errorMessage.contains("检测") || errorMessage.contains("blocked") -> ErrorHandler.ErrorType.DETECTION_ERROR
                    else -> ErrorHandler.ErrorType.PARSING_ERROR
                }

                handleError(errorMessage, errorType)
            }
        } catch (e: Exception) {
            handleError("数据格式错误: ${e.message}", ErrorHandler.ErrorType.PARSING_ERROR, e)
        }
    }

    private fun handleError(message: String, type: ErrorHandler.ErrorType, cause: Throwable? = null) {
        val errorInfo = ErrorHandler.analyzeError(message, cause).copy(
            type = type,
            retryCount = retryCount
        )

        ErrorHandler.recordError(type)

        if (retryCount < maxRetries && errorInfo.retryable) {
            retryCount++
            Log.w(TAG, "Retrying extraction (attempt $retryCount): $message")
            onRetry(errorInfo)

            // 延迟后重试
            CoroutineScope(Dispatchers.Main).launch {
                delay(ErrorHandler.calculateRetryDelay(retryCount - 1))
                isExtracting = false // 允许重新提取
            }
        } else {
            val friendlyMessage = ErrorHandler.getUserFriendlyMessage(errorInfo)
            Log.e(TAG, "Final error after $retryCount retries: $friendlyMessage", cause)
            onError(friendlyMessage)
            isExtracting = false
        }
    }
    
    private fun parseUserDataFromJson(json: String): XhsUserData {
        // 简单的字符串解析，实际项目中建议使用Gson
        val userNameMatch = Regex("\"userName\":\"([^\"]+)\"").find(json)
        val lastPostTimeMatch = Regex("\"lastPostTime\":\"([^\"]+)\"").find(json)
        val lastPostTitleMatch = Regex("\"lastPostTitle\":\"([^\"]+)\"").find(json)
        
        return XhsUserData(
            userName = userNameMatch?.groupValues?.get(1) ?: "",
            lastPostTime = lastPostTimeMatch?.groupValues?.get(1) ?: "",
            lastPostTitle = lastPostTitleMatch?.groupValues?.get(1) ?: "",
            extractTime = System.currentTimeMillis()
        )
    }
    
    private fun getDataExtractionScript(): String {
        return """
            (function() {
                console.log('Starting data extraction script');

                try {
                    // 记录页面基本信息
                    console.log('Current URL:', window.location.href);
                    console.log('Page title:', document.title);
                    console.log('DOM ready state:', document.readyState);

                    // 等待页面完全加载
                    if (document.readyState !== 'complete') {
                        console.log('Page not fully loaded, waiting...');
                        return JSON.stringify({
                            success: false,
                            error: '页面尚未完全加载'
                        });
                    }

                    // 更全面的选择器策略 - 针对小红书最新页面结构
                    const userNameSelectors = [
                        // 小红书特定选择器
                        '.user-name', '.username', '.nick-name', '.nickname',
                        '[data-testid="user-name"]', '.profile-name', '.user-info .name',
                        '.author-name', '.profile-info .name',
                        // 通用选择器
                        'h1', 'h2', '.name', '.title',
                        // 可能的新结构
                        '[class*="name"]', '[class*="user"]', '[class*="profile"]'
                    ];

                    const timeSelectors = [
                        // 小红书时间相关选择器
                        '.time-text', '.publish-time', '.post-time', '.create-time',
                        '[data-time]', '.note-time', '.timestamp', 'time',
                        '.date', '.publish-date', '.creation-time', '.post-date',
                        // 更多可能的时间选择器
                        '[class*="time"]', '[class*="date"]', '[class*="publish"]',
                        '.note-item time', '.feed-item time', '.post-item time',
                        // 相对时间文本
                        '*[text()*="分钟前"]', '*[text()*="小时前"]', '*[text()*="天前"]',
                        '*[text()*="昨天"]', '*[text()*="前天"]'
                    ];

                    const titleSelectors = [
                        '.note-title', '.post-title', '.title', '.content-title',
                        'h1', 'h2', 'h3', '.note-content', '.post-content'
                    ];

                    function findElement(selectors, description) {
                        console.log('Searching for', description, 'with selectors:', selectors);

                        for (let selector of selectors) {
                            const elements = document.querySelectorAll(selector);
                            console.log('Selector', selector, 'found', elements.length, 'elements');

                            for (let element of elements) {
                                const text = element.textContent?.trim();
                                if (text && text.length > 0) {
                                    console.log('Found', description, 'with selector', selector, ':', text);
                                    return element;
                                }
                            }
                        }
                        console.log('No', description, 'found');
                        return null;
                    }

                    // 专门查找时间信息的增强函数
                    function findTimeElement() {
                        console.log('Starting enhanced time search...');

                        // 首先尝试标准选择器
                        let timeElement = findElement(timeSelectors, 'time');
                        if (timeElement) return timeElement;

                        // 查找包含时间文本的元素
                        const timePatterns = [
                            /\d+分钟前/, /\d+小时前/, /\d+天前/,
                            /昨天/, /前天/, /\d{4}-\d{2}-\d{2}/,
                            /\d{2}:\d{2}/, /\d{1,2}月\d{1,2}日/
                        ];

                        const allElements = document.querySelectorAll('*');
                        for (let element of allElements) {
                            const text = element.textContent;
                            if (text && text.length < 50) { // 避免长文本
                                for (let pattern of timePatterns) {
                                    if (pattern.test(text)) {
                                        console.log('Found time by pattern:', text, 'in element:', element.tagName);
                                        return element;
                                    }
                                }
                            }
                        }

                        return null;
                    }

                    // 查找最新笔记的时间信息
                    function findLatestNoteTime() {
                        console.log('Searching for latest note time...');

                        // 查找笔记列表容器
                        const noteContainers = [
                            '.note-list', '.feed-list', '.user-notes', '.note-container',
                            '[class*="note"]', '[class*="feed"]', '[class*="list"]'
                        ];

                        for (let containerSelector of noteContainers) {
                            const containers = document.querySelectorAll(containerSelector);
                            for (let container of containers) {
                                // 在容器中查找第一个时间元素
                                const timeInContainer = container.querySelector('time, [class*="time"], [class*="date"]');
                                if (timeInContainer && timeInContainer.textContent.trim()) {
                                    console.log('Found time in container:', timeInContainer.textContent.trim());
                                    return timeInContainer;
                                }
                            }
                        }

                        return null;
                    }

                    // 尝试查找各种元素
                    const userNameElement = findElement(userNameSelectors, 'username');
                    let timeElement = findTimeElement() || findLatestNoteTime();
                    const titleElement = findElement(titleSelectors, 'title');

                    // 记录页面结构信息用于调试
                    const pageInfo = {
                        url: window.location.href,
                        title: document.title,
                        bodyClasses: document.body.className,
                        allElements: document.querySelectorAll('*').length,
                        hasUserInfo: !!userNameElement,
                        hasTimeInfo: !!timeElement,
                        hasTitleInfo: !!titleElement
                    };

                    console.log('Page analysis:', pageInfo);

                    // 如果没有找到关键信息，返回页面结构信息用于调试
                    if (!userNameElement && !timeElement) {
                        return JSON.stringify({
                            success: false,
                            error: '页面结构不匹配，未找到用户信息',
                            debug: pageInfo,
                            availableClasses: Array.from(document.querySelectorAll('[class]')).slice(0, 10).map(el => el.className),
                            availableIds: Array.from(document.querySelectorAll('[id]')).slice(0, 10).map(el => el.id)
                        });
                    }

                    // 提取更详细的时间信息
                    let extractedTime = '';
                    if (timeElement) {
                        extractedTime = timeElement.getAttribute('data-time') ||
                                       timeElement.getAttribute('datetime') ||
                                       timeElement.textContent.trim();
                    }

                    const result = {
                        success: true,
                        userName: userNameElement ? userNameElement.textContent.trim() : '',
                        lastPostTime: extractedTime,
                        lastPostTitle: titleElement ? titleElement.textContent.trim() : '',
                        url: window.location.href,
                        timestamp: Date.now(),
                        debug: {
                            ...pageInfo,
                            userNameSelector: userNameElement ? userNameElement.tagName + '.' + userNameElement.className : 'none',
                            timeSelector: timeElement ? timeElement.tagName + '.' + timeElement.className : 'none',
                            titleSelector: titleElement ? titleElement.tagName + '.' + titleElement.className : 'none',
                            timeElementText: timeElement ? timeElement.textContent.trim() : 'none',
                            timeElementAttributes: timeElement ? Object.keys(timeElement.attributes).map(key => timeElement.attributes[key].name + '=' + timeElement.attributes[key].value) : []
                        }
                    };

                    console.log('Extraction result:', result);
                    return JSON.stringify(result);

                } catch (error) {
                    console.error('Script error:', error);
                    return JSON.stringify({
                        success: false,
                        error: error.message,
                        stack: error.stack
                    });
                }
            })();
        """.trimIndent()
    }
}

data class XhsUserData(
    val userName: String,
    val lastPostTime: String,
    val lastPostTitle: String,
    val extractTime: Long
)
