2025-08-07 23:43:51.294  3736-3736  UserAction              com.xue.hongshu                      I  用户操作: 启动WebView检查 - 员工ID: fb69f122-84a9-43ba-93f6-6de78f6c2fb8
2025-08-07 23:43:51.296  3736-3736  UserAction              com.xue.hongshu                      I  用户操作: 启动WebView - 员工: 薛博文, 小红书ID: 64d1afd0000000000b007f59
2025-08-07 23:43:51.310  3736-3736  ActivityThread          com.xue.hongshu                      W  handleWindowVisibility: no activity for token android.os.BinderProxy@4e48e5a
2025-08-07 23:43:51.350  3736-3736  com.xue.hongsh          com.xue.hongshu                      W  Accessing hidden method Landroid/view/RenderNode;->getScaleX()F (dark greylist, linking)
2025-08-07 23:43:51.377  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:51.415  3736-3766  EGL_emulation           com.xue.hongshu                      E  tid 3766: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-07 23:43:51.415  3736-3766  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x763878a0dd80, error=EGL_BAD_MATCH
2025-08-07 23:43:51.419  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(73)] "Enhanced anti-detection script loaded", source:  (73)
2025-08-07 23:43:51.419  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(30)] "Bypass detection script loaded", source:  (30)
2025-08-07 23:43:51.444  3736-3766  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:51.447  3736-3766  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000087fe
2025-08-07 23:43:51.448  3736-3766  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:51.448  3736-3766  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:51.585  3736-3766  OpenGLRenderer          com.xue.hongshu                      D  endAllActiveAnimators on 0x76388a307200 (UnprojectedRipple) with handle 0x763878c52360
2025-08-07 23:43:51.622  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Page started loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-07 23:43:51.639  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(0)] "Mixed Content: The page at 'https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59' was loaded over HTTPS, but requested an insecure image 'http://sns-avatar-qc.xhscdn.com/user_banner/1040g2k031i1bh3qin0dg5p6hlv82ovqp82bj5eo?imageView2/2/w/540/format/jpg'. This content should also be served over HTTPS.", source: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59 (0)
2025-08-07 23:43:51.640  3736-3736  XhsWebViewClient        com.xue.hongshu                      E  WebView error: net::ERR_CLEARTEXT_NOT_PERMITTED for http://sns-avatar-qc.xhscdn.com/user_banner/1040g2k031i1bh3qin0dg5p6hlv82ovqp82bj5eo?imageView2/2/w/540/format/jpg
2025-08-07 23:43:51.640  3736-3736  DataExtraction          com.xue.hongshu                      W  数据抓取: 员工ID=fb69f122-84a9-43ba-93f6-6de78f6c2fb8, 结果=失败, 详情=页面加载失败: net::ERR_CLEARTEXT_NOT_PERMITTED
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  java.lang.Exception: Toast callstack! strTip=检查失败: 页面加载失败: net::ERR_CLEARTEXT_NOT_PERMITTED
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  建议:
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  1. 检查网络连接
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  2. 确认用户ID正确
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  3. 稍后重试
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at android.widget.Toast.show(Toast.java:143)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleError$1.invokeSuspend(WebViewActivity.kt:121)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.internal.DispatchedContinuationKt.resumeCancellableWith(DispatchedContinuation.kt:363)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable(Cancellable.kt:26)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.intrinsics.CancellableKt.startCoroutineCancellable$default(Cancellable.kt:21)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.CoroutineStart.invoke(CoroutineStart.kt:88)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.AbstractCoroutine.start(AbstractCoroutine.kt:123)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch(Builders.common.kt:52)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.BuildersKt.launch(Unknown Source:1)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.BuildersKt__Builders_commonKt.launch$default(Builders.common.kt:43)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.BuildersKt.launch$default(Unknown Source:1)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity.handleError(WebViewActivity.kt:115)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity.access$handleError(WebViewActivity.kt:33)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$onCreate$1$1.invoke$lambda$3$lambda$2(WebViewActivity.kt:76)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$onCreate$1$1.$r8$lambda$Ix4cAt2LnuHj_B2XyNvPNzTkWXE(Unknown Source:0)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$onCreate$1$1$$ExternalSyntheticLambda1.invoke(D8$$SyntheticClass:0)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.webview.XhsWebViewClient.onReceivedError(XhsWebViewClient.kt:88)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at my.a(chromium-SystemWebView.apk-default-447211456:1)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at L9.handleMessage(chromium-SystemWebView.apk-default-447211456:128)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at android.os.Handler.dispatchMessage(Handler.java:106)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at android.os.Looper.loop(Looper.java:193)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at android.app.ActivityThread.main(ActivityThread.java:6834)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at java.lang.reflect.Method.invoke(Native Method)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
2025-08-07 23:43:51.646  3736-3736  System.err              com.xue.hongshu                      W  	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-08-07 23:43:51.677  3736-3766  EGL_emulation           com.xue.hongshu                      E  tid 3766: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-07 23:43:51.677  3736-3766  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x763878deae80, error=EGL_BAD_MATCH
2025-08-07 23:43:51.723  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "框架和 SDK 输出的日志默认不展示，可在框架配置文件中设置开启，详见 https://doc.weixin.qq.com/doc/w3_AWkASAb9APAr8IdJI5VS0OyqetUE6?scode=ANAAyQcbAAgB8qKjm9AWkASAb9APA", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:51.923  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(3)] "Error", source:  (3)
2025-08-07 23:43:52.006  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:52.014  3736-3814  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 2 lines
2025-08-07 23:43:52.022  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:52.026  3736-3736  chromium                com.xue.hongshu                      E  [ERROR:web_contents_delegate.cc(225)] WebContentsDelegate::CheckMediaAccessPermission: Not supported.
2025-08-07 23:43:52.026  3736-3736  chromium                com.xue.hongshu                      E  [ERROR:web_contents_delegate.cc(225)] WebContentsDelegate::CheckMediaAccessPermission: Not supported.
2025-08-07 23:43:52.030  3736-3802  cr_media                com.xue.hongshu                      W  Requires MODIFY_AUDIO_SETTINGS and RECORD_AUDIO. No audio device will be available for recording
2025-08-07 23:43:52.058  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-07 23:43:52.059  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Waiting 3560ms before extracting data
2025-08-07 23:43:52.339  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(3)] "Error", source:  (3)
2025-08-07 23:43:52.396  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-07 23:43:52.396  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Waiting 3561ms before extracting data
2025-08-07 23:43:52.502  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(3)] "Error", source:  (3)
2025-08-07 23:43:52.846  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.065  3736-3814  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 6 lines
2025-08-07 23:43:53.069  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.087  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Page finished loading: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59
2025-08-07 23:43:53.087  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Waiting 3666ms before extracting data
2025-08-07 23:43:53.151  3736-3802  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-07 23:43:53.151  3736-3802  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-07 23:43:53.151  3736-3802  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-07 23:43:53.151  3736-3802  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-07 23:43:53.153  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.225  3736-3814  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 5 lines
2025-08-07 23:43:53.227  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.232  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-07 23:43:53.232  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-07 23:43:53.233  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.238  3736-3814  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-07 23:43:53.241  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.254  3736-3802  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-07 23:43:53.254  3736-3802  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-07 23:43:53.254  3736-3802  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-07 23:43:53.254  3736-3802  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-07 23:43:53.255  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.258  3736-3814  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:43:53.262  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.289  3736-3814  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-07 23:43:53.292  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.293  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-07 23:43:53.294  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-07 23:43:53.295  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.305  3736-3814  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-07 23:43:53.308  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.309  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:43:53.334  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.334  3736-3802  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-07 23:43:53.334  3736-3802  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-07 23:43:53.334  3736-3802  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-07 23:43:53.335  3736-3802  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-07 23:43:53.342  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.368  3736-3814  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-07 23:43:53.370  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.372  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-07 23:43:53.374  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-07 23:43:53.374  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.381  3736-3814  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-07 23:43:53.383  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.389  3736-3814  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:43:53.398  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.398  3736-3802  <no-tag>                com.xue.hongshu                      D  PlayerBase::PlayerBase()
2025-08-07 23:43:53.399  3736-3802  <no-tag>                com.xue.hongshu                      D  TrackPlayerBase::TrackPlayerBase()
2025-08-07 23:43:53.399  3736-3802  libOpenSLES             com.xue.hongshu                      I  Emulating old channel mask behavior (ignoring positional mask 0x3, using default mask 0x3 based on channel count of 2)
2025-08-07 23:43:53.399  3736-3802  AudioTrack              com.xue.hongshu                      W  AUDIO_OUTPUT_FLAG_FAST denied by server; frameCount 0 -> 2052
2025-08-07 23:43:53.404  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.441  3736-3814  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-07 23:43:53.443  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.444  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-07 23:43:53.445  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000084ff
2025-08-07 23:43:53.446  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.452  3736-3814  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) Chrome_InProcGp identical 4 lines
2025-08-07 23:43:53.455  3736-3814  eglCodecCommon          com.xue.hongshu                      E  glUtilsParamSize: unknow param 0x000088ef
2025-08-07 23:43:53.577  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.577  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.577  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.577  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.577  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infra_sec_web_api_walify", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.577  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.577  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] pageView", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.577  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.578  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] visitMobile", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.578  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.581  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] AbTestPluginMetrics", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.581  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.581  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] AbTestPluginMetrics", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.581  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.581  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.581  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.581  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.581  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.581  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.581  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.585  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.585  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.585  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.585  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.585  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.585  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.585  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.585  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.585  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] wxOpenAppTagStatus", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.585  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.588  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.588  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.588  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infra_sec_web_api_walify", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.588  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.588  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.588  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.588  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.588  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.588  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.588  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.591  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.591  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.591  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.591  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.591  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.591  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.591  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.591  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.591  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.591  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.635  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.635  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.635  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.635  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.635  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.635  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.635  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.636  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.644  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserMemory", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.644  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.644  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserNetwork", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.644  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.644  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserNavigationTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.644  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.644  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserScriptsExecutionTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.644  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.644  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] browserRenderTimes", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:53.644  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:55.619  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-07 23:43:55.619  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-07 23:43:55.958  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Already extracting, returning
2025-08-07 23:43:56.119  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-07 23:43:56.119  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-07 23:43:56.119  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-07 23:43:56.119  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-07 23:43:56.120  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(50)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (50)
2025-08-07 23:43:56.120  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .user-name found 1 elements", source:  (54)
2025-08-07 23:43:56.120  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(59)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (59)
2025-08-07 23:43:56.121  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(70)] "Starting enhanced time search...", source:  (70)
2025-08-07 23:43:56.121  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(50)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time,*[text()*="分钟前"],*[text()*="小时前"],*[text()*="天前"],*[text()*="昨天"],*[text()*="前天"]", source:  (50)
2025-08-07 23:43:56.121  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .time-text found 0 elements", source:  (54)
2025-08-07 23:43:56.121  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .publish-time found 0 elements", source:  (54)
2025-08-07 23:43:56.121  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .post-time found 0 elements", source:  (54)
2025-08-07 23:43:56.121  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .create-time found 0 elements", source:  (54)
2025-08-07 23:43:56.121  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector [data-time] found 0 elements", source:  (54)
2025-08-07 23:43:56.121  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .note-time found 0 elements", source:  (54)
2025-08-07 23:43:56.122  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .timestamp found 0 elements", source:  (54)
2025-08-07 23:43:56.122  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector time found 0 elements", source:  (54)
2025-08-07 23:43:56.122  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .date found 0 elements", source:  (54)
2025-08-07 23:43:56.122  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .publish-date found 0 elements", source:  (54)
2025-08-07 23:43:56.122  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .creation-time found 0 elements", source:  (54)
2025-08-07 23:43:56.122  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .post-date found 0 elements", source:  (54)
2025-08-07 23:43:56.123  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector [class*="time"] found 0 elements", source:  (54)
2025-08-07 23:43:56.123  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector [class*="date"] found 0 elements", source:  (54)
2025-08-07 23:43:56.123  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector [class*="publish"] found 0 elements", source:  (54)
2025-08-07 23:43:56.123  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .note-item time found 0 elements", source:  (54)
2025-08-07 23:43:56.123  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .feed-item time found 0 elements", source:  (54)
2025-08-07 23:43:56.123  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .post-item time found 0 elements", source:  (54)
2025-08-07 23:43:56.124  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(182)] "Script error: [object DOMException]", source:  (182)
2025-08-07 23:43:56.124  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-07 23:43:56.124  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":false,\"error\":\"Failed to execute 'querySelectorAll' on 'Document': '*[text()*=\\\"分钟前\\\"]' is not a valid selector.\",\"stack\":\"Error: Failed to execute 'querySelectorAll' on 'Document': '*[text()*=\\\"分钟前\\\"]' is not a valid selector.\\n    at findElement (\u003Canonymous>:53:43)\\n    at findTimeElement (\u003Canonymous>:73:31)\\n    at \u003Canonymous>:126:27\\n    at \u003Canonymous>:189:3\"}"
2025-08-07 23:43:56.125  3736-3736  ErrorHandler            com.xue.hongshu                      D  Error stats: {PARSING_ERROR=3}
2025-08-07 23:43:56.125  3736-3736  XhsWebViewClient        com.xue.hongshu                      W  Retrying extraction (attempt 1): Failed to execute 'querySelectorAll' on 'Document': '*[text()*=\\
2025-08-07 23:43:56.250  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(3)] "Error", source:  (3)
2025-08-07 23:43:56.264  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://dhjaibcmgocbpegmejfphjhhpaphmkpp/is.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-07 23:43:56.265  3736-3736  XhsWebViewClient        com.xue.hongshu                      E  WebView error: net::ERR_CONNECTION_REFUSED for http://127.0.0.1:9222/json/version
2025-08-07 23:43:56.265  3736-3736  DataExtraction          com.xue.hongshu                      W  数据抓取: 员工ID=fb69f122-84a9-43ba-93f6-6de78f6c2fb8, 结果=失败, 详情=无法连接到服务器
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  java.lang.Exception: Toast callstack! strTip=检查失败: 无法连接到服务器
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  建议:
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  1. 检查网络连接
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  2. 确认用户ID正确
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  3. 稍后重试
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  	at android.widget.Toast.show(Toast.java:143)
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleError$1.invokeSuspend(WebViewActivity.kt:121)
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  	at android.os.Handler.handleCallback(Handler.java:873)
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  	at android.os.Handler.dispatchMessage(Handler.java:99)
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  	at android.os.Looper.loop(Looper.java:193)
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  	at android.app.ActivityThread.main(ActivityThread.java:6834)
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  	at java.lang.reflect.Method.invoke(Native Method)
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
2025-08-07 23:43:56.270  3736-3736  System.err              com.xue.hongshu                      W  	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-08-07 23:43:56.272  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://beeaddblkeeialcohiolkkoiifhgjooj/manifest.json. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-07 23:43:56.272  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://ddgooekaaihgnbfbgalfiooiicdnmnia/js/js-index.ts.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-07 23:43:56.272  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://keeelahekhhgkpaipdodgjnmgkfcdpde/inject.6070000c.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-07 23:43:56.272  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://dbichmdlbjdeplpkhcejgkakobjbjalc/content-scripts/xiaohongshu.css. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-07 23:43:56.272  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://piejlhcmefdepbgalcongckfomfobokb/worker.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-07 23:43:56.272  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://lenndnnbdichpjlfmfadcjpaenmiflan/inject.bundle.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-07 23:43:56.272  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "Fetch API cannot load chrome-extension://hmeohemhimcjlegdjloglnkfablbneif/inject.bundle.js. URL scheme must be "http" or "https" for CORS request.", source: https://fe-static.xhscdn.com/as/v1/f218/a23/public/0666f0acdeed38d4cd9084ade1739498.js (1)
2025-08-07 23:43:56.291  3736-3736  XhsWebViewClient        com.xue.hongshu                      E  WebView error: net::ERR_CONNECTION_REFUSED for http://127.0.0.1:54345/
2025-08-07 23:43:56.291  3736-3736  DataExtraction          com.xue.hongshu                      W  数据抓取: 员工ID=fb69f122-84a9-43ba-93f6-6de78f6c2fb8, 结果=失败, 详情=无法连接到服务器
2025-08-07 23:43:56.319  3736-3766  EGL_emulation           com.xue.hongshu                      E  tid 3766: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-07 23:43:56.319  3736-3766  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x763878f37a80, error=EGL_BAD_MATCH
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  java.lang.Exception: Toast callstack! strTip=检查失败: 无法连接到服务器
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  建议:
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  1. 检查网络连接
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  2. 确认用户ID正确
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  3. 稍后重试
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  	at android.widget.Toast.show(Toast.java:143)
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  	at com.xue.hongshu.activity.WebViewActivity$handleError$1.invokeSuspend(WebViewActivity.kt:121)
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  	at android.os.Handler.handleCallback(Handler.java:873)
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  	at android.os.Handler.dispatchMessage(Handler.java:99)
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  	at android.os.Looper.loop(Looper.java:193)
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  	at android.app.ActivityThread.main(ActivityThread.java:6834)
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  	at java.lang.reflect.Method.invoke(Native Method)
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:493)
2025-08-07 23:43:56.323  3736-3736  System.err              com.xue.hongshu                      W  	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:860)
2025-08-07 23:43:56.374  3736-3766  EGL_emulation           com.xue.hongshu                      E  tid 3766: eglSurfaceAttrib(1493): error 0x3009 (EGL_BAD_MATCH)
2025-08-07 23:43:56.374  3736-3766  OpenGLRenderer          com.xue.hongshu                      W  Failed to set EGL_SWAP_BEHAVIOR on surface 0x763878f04d00, error=EGL_BAD_MATCH
2025-08-07 23:43:56.755  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Starting data extraction
2025-08-07 23:43:56.755  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  Executing JavaScript for data extraction
2025-08-07 23:43:56.756  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(2)] "Starting data extraction script", source:  (2)
2025-08-07 23:43:56.756  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(6)] "Current URL: https://www.xiaohongshu.com/user/profile/64d1afd0000000000b007f59", source:  (6)
2025-08-07 23:43:56.756  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(7)] "Page title: @哈尔滨金包银老刘 的个人主页", source:  (7)
2025-08-07 23:43:56.756  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(8)] "DOM ready state: complete", source:  (8)
2025-08-07 23:43:56.756  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(50)] "Searching for username with selectors: .user-name,.username,.nick-name,.nickname,[data-testid="user-name"],.profile-name,.user-info .name,.author-name,.profile-info .name,h1,h2,.name,.title,[class*="name"],[class*="user"],[class*="profile"]", source:  (50)
2025-08-07 23:43:56.756  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .user-name found 1 elements", source:  (54)
2025-08-07 23:43:56.757  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(59)] "Found username with selector .user-name : 哈尔滨金包银老刘", source:  (59)
2025-08-07 23:43:56.757  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(70)] "Starting enhanced time search...", source:  (70)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(50)] "Searching for time with selectors: .time-text,.publish-time,.post-time,.create-time,[data-time],.note-time,.timestamp,time,.date,.publish-date,.creation-time,.post-date,[class*="time"],[class*="date"],[class*="publish"],.note-item time,.feed-item time,.post-item time,*[text()*="分钟前"],*[text()*="小时前"],*[text()*="天前"],*[text()*="昨天"],*[text()*="前天"]", source:  (50)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .time-text found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .publish-time found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .post-time found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .create-time found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector [data-time] found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .note-time found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .timestamp found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector time found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .date found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .publish-date found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .creation-time found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .post-date found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector [class*="time"] found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector [class*="date"] found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector [class*="publish"] found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .note-item time found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .feed-item time found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(54)] "Selector .post-item time found 0 elements", source:  (54)
2025-08-07 23:43:56.758  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(182)] "Script error: [object DOMException]", source:  (182)
2025-08-07 23:43:56.758  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  JavaScript execution completed
2025-08-07 23:43:56.759  3736-3736  XhsWebViewClient        com.xue.hongshu                      D  JavaScript result: "{\"success\":false,\"error\":\"Failed to execute 'querySelectorAll' on 'Document': '*[text()*=\\\"分钟前\\\"]' is not a valid selector.\",\"stack\":\"Error: Failed to execute 'querySelectorAll' on 'Document': '*[text()*=\\\"分钟前\\\"]' is not a valid selector.\\n    at findElement (\u003Canonymous>:53:43)\\n    at findTimeElement (\u003Canonymous>:73:31)\\n    at \u003Canonymous>:126:27\\n    at \u003Canonymous>:189:3\"}"
2025-08-07 23:43:56.759  3736-3736  ErrorHandler            com.xue.hongshu                      D  Error stats: {PARSING_ERROR=4}
2025-08-07 23:43:56.759  3736-3736  XhsWebViewClient        com.xue.hongshu                      W  Retrying extraction (attempt 2): Failed to execute 'querySelectorAll' on 'Document': '*[text()*=\\
2025-08-07 23:43:57.606  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:43:58.343  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:43:58.646  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:58.647  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:58.647  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:58.647  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:58.647  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] infraBrowserResourceTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:58.647  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:58.647  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "[apm] httpRequestTiming", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:58.647  3736-3736  chromium                com.xue.hongshu                      I  [INFO:CONSOLE(1)] "console.groupEnd", source: https://fe-static.xhscdn.com/formula-static/ranchi/public/js/vendor.a77c6b0.js (1)
2025-08-07 23:43:59.096  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:43:59.837  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:00.601  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:04.331  3736-3766  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) RenderThread identical 5 lines
2025-08-07 23:44:05.069  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:05.819  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:09.550  3736-3766  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) RenderThread identical 5 lines
2025-08-07 23:44:10.296  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:10.765  3736-3736  RenderThread            com.xue.hongshu                      I  type=1400 audit(0.0:1066): avc: denied { ioctl } for path="/dev/fastpipe" dev="tmpfs" ino=6210 ioctlcmd=6867 scontext=u:r:untrusted_app:s0:c52,c256,c512,c768 tcontext=u:object_r:device:s0 tclass=chr_file permissive=1
2025-08-07 23:44:11.039  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:13.264  3736-3766  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) RenderThread identical 3 lines
2025-08-07 23:44:14.015  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:14.769  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:15.506  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:16.261  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:16.993  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:17.746  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:18.483  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:19.235  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:22.213  3736-3766  chatty                  com.xue.hongshu                      I  uid=10052(com.xue.hongshu) RenderThread identical 4 lines
2025-08-07 23:44:22.964  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.
2025-08-07 23:44:23.167  3736-3802  <no-tag>                com.xue.hongshu                      D  PlayerBase::stop() from IPlayer
2025-08-07 23:44:23.167  3736-3802  AudioTrack              com.xue.hongshu                      D  stop() called with 1442820 frames delivered
2025-08-07 23:44:23.285  3736-3802  <no-tag>                com.xue.hongshu                      D  PlayerBase::stop() from IPlayer
2025-08-07 23:44:23.285  3736-3802  AudioTrack              com.xue.hongshu                      D  stop() called with 1443844 frames delivered
2025-08-07 23:44:23.374  3736-3802  <no-tag>                com.xue.hongshu                      D  PlayerBase::stop() from IPlayer
2025-08-07 23:44:23.374  3736-3802  AudioTrack              com.xue.hongshu                      D  stop() called with 1444800 frames delivered
2025-08-07 23:44:23.438  3736-3802  <no-tag>                com.xue.hongshu                      D  PlayerBase::stop() from IPlayer
2025-08-07 23:44:23.438  3736-3802  AudioTrack              com.xue.hongshu                      D  stop() called with 1443844 frames delivered
2025-08-07 23:44:23.736  3736-3766  HostConnection          com.xue.hongshu                      D  glGetError exceeded.