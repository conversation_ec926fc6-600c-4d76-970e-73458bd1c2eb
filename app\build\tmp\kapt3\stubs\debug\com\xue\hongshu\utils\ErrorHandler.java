package com.xue.hongshu.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0003\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010$\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0002()B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u00042\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\rJ\u000e\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\bJ\u0006\u0010\u0011\u001a\u00020\u0012J\u0086\u0001\u0010\u0013\u001a\b\u0012\u0004\u0012\u0002H\u00150\u0014\"\u0004\b\u0000\u0010\u00152\u001c\u0010\u0016\u001a\u0018\b\u0001\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00150\u0018\u0012\u0006\u0012\u0004\u0018\u00010\u00010\u00172\u0018\b\u0002\u0010\u0019\u001a\u0012\u0012\b\u0012\u00060\u001aj\u0002`\u001b\u0012\u0004\u0012\u00020\n0\u00172\u0014\b\u0002\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00120\u00172\u0014\b\u0002\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00120\u0017H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001e\u0010\u001fJ\u0012\u0010 \u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b0!J\u000e\u0010\"\u001a\u00020\u00042\u0006\u0010#\u001a\u00020\nJ\u000e\u0010$\u001a\u00020\u00122\u0006\u0010%\u001a\u00020\u0007J\u000e\u0010&\u001a\u00020\'2\u0006\u0010%\u001a\u00020\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006*"}, d2 = {"Lcom/xue/hongshu/utils/ErrorHandler;", "", "()V", "TAG", "", "errorStats", "", "Lcom/xue/hongshu/utils/ErrorHandler$ErrorType;", "", "analyzeError", "Lcom/xue/hongshu/utils/ErrorHandler$ErrorInfo;", "error", "cause", "", "calculateRetryDelay", "", "retryCount", "clearErrorStats", "", "executeWithRetry", "Lkotlin/Result;", "T", "operation", "Lkotlin/Function1;", "Lkotlin/coroutines/Continuation;", "errorAnalyzer", "Ljava/lang/Exception;", "Lkotlin/Exception;", "onRetry", "onFinalError", "executeWithRetry-yxL6bBk", "(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getErrorStats", "", "getUserFriendlyMessage", "errorInfo", "recordError", "errorType", "shouldSwitchStrategy", "", "ErrorInfo", "ErrorType", "app_debug"})
public final class ErrorHandler {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ErrorHandler";
    
    /**
     * 记录错误统计
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Map<com.xue.hongshu.utils.ErrorHandler.ErrorType, java.lang.Integer> errorStats = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.xue.hongshu.utils.ErrorHandler INSTANCE = null;
    
    private ErrorHandler() {
        super();
    }
    
    /**
     * 分析错误类型
     */
    @org.jetbrains.annotations.NotNull()
    public final com.xue.hongshu.utils.ErrorHandler.ErrorInfo analyzeError(@org.jetbrains.annotations.NotNull()
    java.lang.String error, @org.jetbrains.annotations.Nullable()
    java.lang.Throwable cause) {
        return null;
    }
    
    /**
     * 获取用户友好的错误消息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUserFriendlyMessage(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.utils.ErrorHandler.ErrorInfo errorInfo) {
        return null;
    }
    
    /**
     * 计算重试延迟（指数退避 + 随机抖动）
     */
    public final long calculateRetryDelay(int retryCount) {
        return 0L;
    }
    
    public final void recordError(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.utils.ErrorHandler.ErrorType errorType) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<com.xue.hongshu.utils.ErrorHandler.ErrorType, java.lang.Integer> getErrorStats() {
        return null;
    }
    
    public final void clearErrorStats() {
    }
    
    /**
     * 检查是否需要切换策略
     */
    public final boolean shouldSwitchStrategy(@org.jetbrains.annotations.NotNull()
    com.xue.hongshu.utils.ErrorHandler.ErrorType errorType) {
        return false;
    }
    
    /**
     * 错误信息数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0003\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0019\b\u0087\b\u0018\u00002\u00020\u0001B?\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u000bH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u000bH\u00c6\u0003JG\u0010\u001f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010 \u001a\u00020\t2\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\"\u001a\u00020\u000bH\u00d6\u0001J\t\u0010#\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\f\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018\u00a8\u0006$"}, d2 = {"Lcom/xue/hongshu/utils/ErrorHandler$ErrorInfo;", "", "type", "Lcom/xue/hongshu/utils/ErrorHandler$ErrorType;", "message", "", "cause", "", "retryable", "", "retryCount", "", "maxRetries", "(Lcom/xue/hongshu/utils/ErrorHandler$ErrorType;Ljava/lang/String;Ljava/lang/Throwable;ZII)V", "getCause", "()Ljava/lang/Throwable;", "getMaxRetries", "()I", "getMessage", "()Ljava/lang/String;", "getRetryCount", "getRetryable", "()Z", "getType", "()Lcom/xue/hongshu/utils/ErrorHandler$ErrorType;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class ErrorInfo {
        @org.jetbrains.annotations.NotNull()
        private final com.xue.hongshu.utils.ErrorHandler.ErrorType type = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String message = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.Throwable cause = null;
        private final boolean retryable = false;
        private final int retryCount = 0;
        private final int maxRetries = 0;
        
        public ErrorInfo(@org.jetbrains.annotations.NotNull()
        com.xue.hongshu.utils.ErrorHandler.ErrorType type, @org.jetbrains.annotations.NotNull()
        java.lang.String message, @org.jetbrains.annotations.Nullable()
        java.lang.Throwable cause, boolean retryable, int retryCount, int maxRetries) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.utils.ErrorHandler.ErrorType getType() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Throwable getCause() {
            return null;
        }
        
        public final boolean getRetryable() {
            return false;
        }
        
        public final int getRetryCount() {
            return 0;
        }
        
        public final int getMaxRetries() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.utils.ErrorHandler.ErrorType component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Throwable component3() {
            return null;
        }
        
        public final boolean component4() {
            return false;
        }
        
        public final int component5() {
            return 0;
        }
        
        public final int component6() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.utils.ErrorHandler.ErrorInfo copy(@org.jetbrains.annotations.NotNull()
        com.xue.hongshu.utils.ErrorHandler.ErrorType type, @org.jetbrains.annotations.NotNull()
        java.lang.String message, @org.jetbrains.annotations.Nullable()
        java.lang.Throwable cause, boolean retryable, int retryCount, int maxRetries) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 错误类型枚举
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/xue/hongshu/utils/ErrorHandler$ErrorType;", "", "(Ljava/lang/String;I)V", "NETWORK_ERROR", "PARSING_ERROR", "PAGE_LOAD_ERROR", "DETECTION_ERROR", "TIMEOUT_ERROR", "SSL_ERROR", "CERTIFICATE_ERROR", "UNKNOWN_ERROR", "app_debug"})
    public static enum ErrorType {
        /*public static final*/ NETWORK_ERROR /* = new NETWORK_ERROR() */,
        /*public static final*/ PARSING_ERROR /* = new PARSING_ERROR() */,
        /*public static final*/ PAGE_LOAD_ERROR /* = new PAGE_LOAD_ERROR() */,
        /*public static final*/ DETECTION_ERROR /* = new DETECTION_ERROR() */,
        /*public static final*/ TIMEOUT_ERROR /* = new TIMEOUT_ERROR() */,
        /*public static final*/ SSL_ERROR /* = new SSL_ERROR() */,
        /*public static final*/ CERTIFICATE_ERROR /* = new CERTIFICATE_ERROR() */,
        /*public static final*/ UNKNOWN_ERROR /* = new UNKNOWN_ERROR() */;
        
        ErrorType() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.xue.hongshu.utils.ErrorHandler.ErrorType> getEntries() {
            return null;
        }
    }
}