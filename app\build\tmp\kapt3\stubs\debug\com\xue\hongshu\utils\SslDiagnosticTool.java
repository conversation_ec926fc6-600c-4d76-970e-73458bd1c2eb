package com.xue.hongshu.utils;

/**
 * SSL连接诊断工具
 * 用于检测和诊断SSL连接问题
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0005\u001e\u001f !\"B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\"\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\u0006H\u0086@\u00a2\u0006\u0002\u0010\tJ \u0010\n\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\u00042\b\b\u0002\u0010\f\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u000eJ\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\u0002J\u0014\u0010\u0013\u001a\u0004\u0018\u00010\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u0002J \u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\u00062\u0006\u0010\u0018\u001a\u00020\u00102\b\u0010\u0019\u001a\u0004\u0018\u00010\u0014H\u0002J\u0018\u0010\u001a\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\u00042\u0006\u0010\u001c\u001a\u00020\u001dH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006#"}, d2 = {"Lcom/xue/hongshu/utils/SslDiagnosticTool;", "", "()V", "TAG", "", "diagnoseBatch", "", "Lcom/xue/hongshu/utils/SslDiagnosticTool$SslDiagnosticResult;", "urls", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "diagnoseSslConnection", "urlString", "timeout", "", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "extractCertificateInfo", "Lcom/xue/hongshu/utils/SslDiagnosticTool$CertificateInfo;", "certificate", "Ljava/security/cert/X509Certificate;", "extractConnectionInfo", "Lcom/xue/hongshu/utils/SslDiagnosticTool$ConnectionInfo;", "sslSession", "Ljavax/net/ssl/SSLSession;", "generateRecommendations", "certInfo", "connInfo", "handleSslException", "url", "e", "Ljavax/net/ssl/SSLException;", "CertificateInfo", "ConnectionInfo", "DiagnosticHostnameVerifier", "DiagnosticTrustManager", "SslDiagnosticResult", "app_debug"})
public final class SslDiagnosticTool {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "SslDiagnosticTool";
    @org.jetbrains.annotations.NotNull()
    public static final com.xue.hongshu.utils.SslDiagnosticTool INSTANCE = null;
    
    private SslDiagnosticTool() {
        super();
    }
    
    /**
     * 诊断SSL连接
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object diagnoseSslConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String urlString, int timeout, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.xue.hongshu.utils.SslDiagnosticTool.SslDiagnosticResult> $completion) {
        return null;
    }
    
    /**
     * 批量诊断多个URL
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object diagnoseBatch(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> urls, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.xue.hongshu.utils.SslDiagnosticTool.SslDiagnosticResult>> $completion) {
        return null;
    }
    
    /**
     * 提取证书信息
     */
    private final com.xue.hongshu.utils.SslDiagnosticTool.CertificateInfo extractCertificateInfo(java.security.cert.X509Certificate certificate) {
        return null;
    }
    
    /**
     * 提取连接信息
     */
    private final com.xue.hongshu.utils.SslDiagnosticTool.ConnectionInfo extractConnectionInfo(javax.net.ssl.SSLSession sslSession) {
        return null;
    }
    
    /**
     * 处理SSL异常
     */
    private final com.xue.hongshu.utils.SslDiagnosticTool.SslDiagnosticResult handleSslException(java.lang.String url, javax.net.ssl.SSLException e) {
        return null;
    }
    
    /**
     * 生成建议
     */
    private final java.util.List<java.lang.String> generateRecommendations(com.xue.hongshu.utils.SslDiagnosticTool.CertificateInfo certInfo, com.xue.hongshu.utils.SslDiagnosticTool.ConnectionInfo connInfo) {
        return null;
    }
    
    /**
     * 证书信息
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\b\u001a\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001BE\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\u0003\u0012\u0006\u0010\r\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u000bH\u00c6\u0003J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003JY\u0010\"\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u00032\b\b\u0002\u0010\r\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010#\u001a\u00020\t2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0011R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0013R\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0013R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0018\u00a8\u0006("}, d2 = {"Lcom/xue/hongshu/utils/SslDiagnosticTool$CertificateInfo;", "", "subject", "", "issuer", "validFrom", "Ljava/util/Date;", "validTo", "isExpired", "", "daysUntilExpiry", "", "signatureAlgorithm", "serialNumber", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/Date;Ljava/util/Date;ZJLjava/lang/String;Ljava/lang/String;)V", "getDaysUntilExpiry", "()J", "()Z", "getIssuer", "()Ljava/lang/String;", "getSerialNumber", "getSignatureAlgorithm", "getSubject", "getValidFrom", "()Ljava/util/Date;", "getValidTo", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class CertificateInfo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String subject = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String issuer = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.Date validFrom = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.Date validTo = null;
        private final boolean isExpired = false;
        private final long daysUntilExpiry = 0L;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String signatureAlgorithm = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String serialNumber = null;
        
        public CertificateInfo(@org.jetbrains.annotations.NotNull()
        java.lang.String subject, @org.jetbrains.annotations.NotNull()
        java.lang.String issuer, @org.jetbrains.annotations.NotNull()
        java.util.Date validFrom, @org.jetbrains.annotations.NotNull()
        java.util.Date validTo, boolean isExpired, long daysUntilExpiry, @org.jetbrains.annotations.NotNull()
        java.lang.String signatureAlgorithm, @org.jetbrains.annotations.NotNull()
        java.lang.String serialNumber) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSubject() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getIssuer() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Date getValidFrom() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Date getValidTo() {
            return null;
        }
        
        public final boolean isExpired() {
            return false;
        }
        
        public final long getDaysUntilExpiry() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSignatureAlgorithm() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getSerialNumber() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Date component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.Date component4() {
            return null;
        }
        
        public final boolean component5() {
            return false;
        }
        
        public final long component6() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component7() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component8() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.utils.SslDiagnosticTool.CertificateInfo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String subject, @org.jetbrains.annotations.NotNull()
        java.lang.String issuer, @org.jetbrains.annotations.NotNull()
        java.util.Date validFrom, @org.jetbrains.annotations.NotNull()
        java.util.Date validTo, boolean isExpired, long daysUntilExpiry, @org.jetbrains.annotations.NotNull()
        java.lang.String signatureAlgorithm, @org.jetbrains.annotations.NotNull()
        java.lang.String serialNumber) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 连接信息
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0087\b\u0018\u00002\u00020\u0001BA\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00030\t\u0012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00030\t\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0007H\u00c6\u0003J\u000f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00030\tH\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00030\tH\u00c6\u0003JQ\u0010\u001b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00030\t2\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00030\tH\u00c6\u0001J\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020\u0007H\u00d6\u0001J\t\u0010 \u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00030\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00030\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\rR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\r\u00a8\u0006!"}, d2 = {"Lcom/xue/hongshu/utils/SslDiagnosticTool$ConnectionInfo;", "", "protocol", "", "cipherSuite", "peerHost", "peerPort", "", "localCertificates", "", "peerCertificates", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/util/List;Ljava/util/List;)V", "getCipherSuite", "()Ljava/lang/String;", "getLocalCertificates", "()Ljava/util/List;", "getPeerCertificates", "getPeerHost", "getPeerPort", "()I", "getProtocol", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
    public static final class ConnectionInfo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String protocol = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String cipherSuite = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String peerHost = null;
        private final int peerPort = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.String> localCertificates = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.String> peerCertificates = null;
        
        public ConnectionInfo(@org.jetbrains.annotations.NotNull()
        java.lang.String protocol, @org.jetbrains.annotations.NotNull()
        java.lang.String cipherSuite, @org.jetbrains.annotations.NotNull()
        java.lang.String peerHost, int peerPort, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> localCertificates, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> peerCertificates) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getProtocol() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getCipherSuite() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getPeerHost() {
            return null;
        }
        
        public final int getPeerPort() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getLocalCertificates() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getPeerCertificates() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component3() {
            return null;
        }
        
        public final int component4() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> component6() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.utils.SslDiagnosticTool.ConnectionInfo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String protocol, @org.jetbrains.annotations.NotNull()
        java.lang.String cipherSuite, @org.jetbrains.annotations.NotNull()
        java.lang.String peerHost, int peerPort, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> localCertificates, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> peerCertificates) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 诊断用主机名验证器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0002\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0016\u00a8\u0006\t"}, d2 = {"Lcom/xue/hongshu/utils/SslDiagnosticTool$DiagnosticHostnameVerifier;", "Ljavax/net/ssl/HostnameVerifier;", "()V", "verify", "", "hostname", "", "session", "Ljavax/net/ssl/SSLSession;", "app_debug"})
    static final class DiagnosticHostnameVerifier implements javax.net.ssl.HostnameVerifier {
        
        public DiagnosticHostnameVerifier() {
            super();
        }
        
        @java.lang.Override()
        public boolean verify(@org.jetbrains.annotations.NotNull()
        java.lang.String hostname, @org.jetbrains.annotations.NotNull()
        javax.net.ssl.SSLSession session) {
            return false;
        }
    }
    
    /**
     * 诊断用信任管理器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\b\u0002\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J#\u0010\u0003\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0016\u00a2\u0006\u0002\u0010\nJ#\u0010\u000b\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0016\u00a2\u0006\u0002\u0010\nJ\u0013\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0016\u00a2\u0006\u0002\u0010\r\u00a8\u0006\u000e"}, d2 = {"Lcom/xue/hongshu/utils/SslDiagnosticTool$DiagnosticTrustManager;", "Ljavax/net/ssl/X509TrustManager;", "()V", "checkClientTrusted", "", "chain", "", "Ljava/security/cert/X509Certificate;", "authType", "", "([Ljava/security/cert/X509Certificate;Ljava/lang/String;)V", "checkServerTrusted", "getAcceptedIssuers", "()[Ljava/security/cert/X509Certificate;", "app_debug"})
    static final class DiagnosticTrustManager implements javax.net.ssl.X509TrustManager {
        
        public DiagnosticTrustManager() {
            super();
        }
        
        @java.lang.Override()
        public void checkClientTrusted(@org.jetbrains.annotations.NotNull()
        java.security.cert.X509Certificate[] chain, @org.jetbrains.annotations.NotNull()
        java.lang.String authType) {
        }
        
        @java.lang.Override()
        public void checkServerTrusted(@org.jetbrains.annotations.NotNull()
        java.security.cert.X509Certificate[] chain, @org.jetbrains.annotations.NotNull()
        java.lang.String authType) {
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
            return null;
        }
    }
    
    /**
     * SSL诊断结果
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0015\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001BI\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003JQ\u0010\u001e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0001J\u0013\u0010\u001f\u001a\u00020\u00052\b\u0010 \u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010!\u001a\u00020\"H\u00d6\u0001J\t\u0010#\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0014R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0013\u00a8\u0006$"}, d2 = {"Lcom/xue/hongshu/utils/SslDiagnosticTool$SslDiagnosticResult;", "", "url", "", "isSuccess", "", "errorMessage", "certificateInfo", "Lcom/xue/hongshu/utils/SslDiagnosticTool$CertificateInfo;", "connectionInfo", "Lcom/xue/hongshu/utils/SslDiagnosticTool$ConnectionInfo;", "recommendations", "", "(Ljava/lang/String;ZLjava/lang/String;Lcom/xue/hongshu/utils/SslDiagnosticTool$CertificateInfo;Lcom/xue/hongshu/utils/SslDiagnosticTool$ConnectionInfo;Ljava/util/List;)V", "getCertificateInfo", "()Lcom/xue/hongshu/utils/SslDiagnosticTool$CertificateInfo;", "getConnectionInfo", "()Lcom/xue/hongshu/utils/SslDiagnosticTool$ConnectionInfo;", "getErrorMessage", "()Ljava/lang/String;", "()Z", "getRecommendations", "()Ljava/util/List;", "getUrl", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class SslDiagnosticResult {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String url = null;
        private final boolean isSuccess = false;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String errorMessage = null;
        @org.jetbrains.annotations.Nullable()
        private final com.xue.hongshu.utils.SslDiagnosticTool.CertificateInfo certificateInfo = null;
        @org.jetbrains.annotations.Nullable()
        private final com.xue.hongshu.utils.SslDiagnosticTool.ConnectionInfo connectionInfo = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<java.lang.String> recommendations = null;
        
        public SslDiagnosticResult(@org.jetbrains.annotations.NotNull()
        java.lang.String url, boolean isSuccess, @org.jetbrains.annotations.Nullable()
        java.lang.String errorMessage, @org.jetbrains.annotations.Nullable()
        com.xue.hongshu.utils.SslDiagnosticTool.CertificateInfo certificateInfo, @org.jetbrains.annotations.Nullable()
        com.xue.hongshu.utils.SslDiagnosticTool.ConnectionInfo connectionInfo, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> recommendations) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUrl() {
            return null;
        }
        
        public final boolean isSuccess() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getErrorMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.xue.hongshu.utils.SslDiagnosticTool.CertificateInfo getCertificateInfo() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.xue.hongshu.utils.SslDiagnosticTool.ConnectionInfo getConnectionInfo() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> getRecommendations() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final boolean component2() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.xue.hongshu.utils.SslDiagnosticTool.CertificateInfo component4() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.xue.hongshu.utils.SslDiagnosticTool.ConnectionInfo component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<java.lang.String> component6() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.xue.hongshu.utils.SslDiagnosticTool.SslDiagnosticResult copy(@org.jetbrains.annotations.NotNull()
        java.lang.String url, boolean isSuccess, @org.jetbrains.annotations.Nullable()
        java.lang.String errorMessage, @org.jetbrains.annotations.Nullable()
        com.xue.hongshu.utils.SslDiagnosticTool.CertificateInfo certificateInfo, @org.jetbrains.annotations.Nullable()
        com.xue.hongshu.utils.SslDiagnosticTool.ConnectionInfo connectionInfo, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> recommendations) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}