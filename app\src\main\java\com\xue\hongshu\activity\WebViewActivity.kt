package com.xue.hongshu.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.webkit.WebView
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.lifecycleScope
import com.xue.hongshu.BuildConfig
import com.xue.hongshu.data.database.AppDatabase
import com.xue.hongshu.repository.EmployeeRepository
import com.xue.hongshu.ui.theme.HongshuTheme
import com.xue.hongshu.utils.Logger
import com.xue.hongshu.webview.AntiDetectionHelper
import com.xue.hongshu.webview.XhsUserData
import com.xue.hongshu.webview.XhsWebViewClient
import kotlinx.coroutines.launch

class WebViewActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_EMPLOYEE_ID = "employee_id"
        private const val EXTRA_XHS_USER_ID = "xhs_user_id"
        
        fun createIntent(context: Context, employeeId: String, xhsUserId: String): Intent {
            return Intent(context, WebViewActivity::class.java).apply {
                putExtra(EXTRA_EMPLOYEE_ID, employeeId)
                putExtra(EXTRA_XHS_USER_ID, xhsUserId)
            }
        }
    }
    
    private lateinit var repository: EmployeeRepository
    private lateinit var employeeId: String
    private lateinit var xhsUserId: String
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        employeeId = intent.getStringExtra(EXTRA_EMPLOYEE_ID) ?: ""
        xhsUserId = intent.getStringExtra(EXTRA_XHS_USER_ID) ?: ""
        
        if (employeeId.isEmpty() || xhsUserId.isEmpty()) {
            Toast.makeText(this, "参数错误", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        
        // 初始化Repository
        val database = AppDatabase.getDatabase(this)
        repository = EmployeeRepository(database.employeeDao())
        
        setContent {
            HongshuTheme {
                WebViewScreen(
                    employeeId = employeeId,
                    xhsUserId = xhsUserId,
                    onDataExtracted = { data ->
                        handleDataExtracted(data)
                    },
                    onError = { error ->
                        handleError(error)
                    },
                    onBackPressed = { finish() }
                )
            }
        }
    }
    
    private fun handleDataExtracted(data: XhsUserData) {
        lifecycleScope.launch {
            try {
                Logger.logDataExtraction(employeeId, true, "用户名: ${data.userName}, 时间: ${data.lastPostTime}")
                repository.updateEmployeeWithXhsData(employeeId, data)

                val timeText = if (data.lastPostTime.isNotEmpty()) data.lastPostTime else "未获取到时间信息"
                val message = "数据更新成功\n用户名: ${data.userName}\n最新发布: $timeText"

                runOnUiThread {
                    try {
                        Toast.makeText(this@WebViewActivity, message, Toast.LENGTH_LONG).show()
                    } catch (e: Exception) {
                        Logger.e("WebViewActivity", "Toast显示失败", e)
                    }
                }
                finish()
            } catch (e: Exception) {
                Logger.e("WebViewActivity", "更新数据失败", e)
                runOnUiThread {
                    try {
                        Toast.makeText(this@WebViewActivity, "更新失败: ${e.message}", Toast.LENGTH_LONG).show()
                    } catch (toastError: Exception) {
                        Logger.e("WebViewActivity", "Toast显示失败", toastError)
                    }
                }
            }
        }
    }
    
    private fun handleError(error: String) {
        lifecycleScope.launch {
            try {
                Logger.logDataExtraction(employeeId, false, error)
                repository.updateEmployeeError(employeeId, error)

                val message = "检查失败: $error\n\n建议:\n1. 检查网络连接\n2. 确认用户ID正确\n3. 稍后重试"
                Toast.makeText(this@WebViewActivity, message, Toast.LENGTH_LONG).show()
            } catch (e: Exception) {
                Logger.e("WebViewActivity", "更新错误状态失败", e)
                Toast.makeText(this@WebViewActivity, "更新错误状态失败: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }
}

@SuppressLint("SetJavaScriptEnabled")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WebViewScreen(
    employeeId: String,
    xhsUserId: String,
    onDataExtracted: (XhsUserData) -> Unit,
    onError: (String) -> Unit,
    onBackPressed: () -> Unit
) {
    var isLoading by remember { mutableStateOf(true) }
    var webView by remember { mutableStateOf<WebView?>(null) }
    var statusMessage by remember { mutableStateOf("正在初始化...") }
    var progress by remember { mutableStateOf(0) }
    val context = LocalContext.current
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("数据检查") },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(
                        onClick = { webView?.reload() }
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            AndroidView(
                factory = { context ->
                    WebView(context).apply {
                        // 仅在调试模式下启用WebView调试
                        if (BuildConfig.DEBUG) {
                            WebView.setWebContentsDebuggingEnabled(true)
                        }

                        // 配置WebView
                        AntiDetectionHelper.configureWebView(this)

                        // 设置WebViewClient
                        webViewClient = XhsWebViewClient(
                            onDataExtracted = { data ->
                                statusMessage = "数据提取成功"
                                onDataExtracted(data)
                            },
                            onError = { error ->
                                statusMessage = "检查失败: $error"
                                onError(error)
                            },
                            onRetry = { errorInfo ->
                                statusMessage = "正在重试... (${errorInfo.retryCount}/${errorInfo.maxRetries})"
                                isLoading = true
                            }
                        )

                        // 设置WebChromeClient来监听进度
                        webChromeClient = object : android.webkit.WebChromeClient() {
                            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                                progress = newProgress
                                if (newProgress < 100) {
                                    statusMessage = "加载中... $newProgress%"
                                } else {
                                    statusMessage = "页面加载完成，正在提取数据..."
                                    isLoading = false
                                }
                            }
                        }
                        
                        // 注入所有反检测脚本
                        AntiDetectionHelper.injectAntiDetectionScript(this)
                        AntiDetectionHelper.bypassCommonDetection(this)

                        // 延迟后添加交互模拟
                        postDelayed({
                            AntiDetectionHelper.simulateHumanScroll(this)
                            AntiDetectionHelper.simulateMouseMovement(this)
                            AntiDetectionHelper.addRandomInteractions(this)
                        }, AntiDetectionHelper.getRandomDelay())

                        // 加载小红书用户页面
                        val url = "https://www.xiaohongshu.com/user/profile/$xhsUserId"
                        loadUrl(url)
                        
                        webView = this
                    }
                },
                modifier = Modifier.fillMaxSize()
            )
            
            // 状态指示器
            if (isLoading || statusMessage.isNotEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Card(
                        modifier = Modifier.padding(16.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(24.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            if (isLoading) {
                                CircularProgressIndicator()
                                Spacer(modifier = Modifier.height(16.dp))
                            }

                            Text(
                                text = statusMessage,
                                style = MaterialTheme.typography.bodyLarge,
                                textAlign = androidx.compose.ui.text.style.TextAlign.Center
                            )

                            if (progress > 0 && progress < 100) {
                                Spacer(modifier = Modifier.height(8.dp))
                                LinearProgressIndicator(
                                    progress = progress / 100f,
                                    modifier = Modifier.fillMaxWidth()
                                )
                            }

                            Spacer(modifier = Modifier.height(16.dp))

                            Text(
                                text = "目标用户ID: $xhsUserId",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
    
    // 监听页面加载状态
    LaunchedEffect(webView) {
        webView?.let { wv ->
            wv.webViewClient = XhsWebViewClient(
                onDataExtracted = onDataExtracted,
                onError = onError,
                onRetry = { isLoading = true }
            )
        }
    }
}
