package com.xue.hongshu.utils

import android.util.Log
import kotlinx.coroutines.delay
import kotlin.random.Random

object ErrorHandler {
    
    private const val TAG = "ErrorHandler"
    
    /**
     * 错误类型枚举
     */
    enum class ErrorType {
        NETWORK_ERROR,      // 网络错误
        PARSING_ERROR,      // 数据解析错误
        PAGE_LOAD_ERROR,    // 页面加载错误
        DETECTION_ERROR,    // 被检测到的错误
        TIMEOUT_ERROR,      // 超时错误
        SSL_ERROR,          // SSL证书错误
        CERTIFICATE_ERROR,  // 证书验证错误
        UNKNOWN_ERROR       // 未知错误
    }
    
    /**
     * 错误信息数据类
     */
    data class ErrorInfo(
        val type: ErrorType,
        val message: String,
        val cause: Throwable? = null,
        val retryable: Boolean = true,
        val retryCount: Int = 0,
        val maxRetries: Int = 3
    )
    
    /**
     * 分析错误类型
     */
    fun analyzeError(error: String, cause: Throwable? = null): ErrorInfo {
        val errorType = when {
            error.contains("网络") || error.contains("network") || error.contains("timeout") -> {
                if (error.contains("timeout")) ErrorType.TIMEOUT_ERROR else ErrorType.NETWORK_ERROR
            }
            error.contains("SSL") || error.contains("ssl") || error.contains("证书") || error.contains("certificate") -> {
                if (error.contains("证书") || error.contains("certificate")) ErrorType.CERTIFICATE_ERROR else ErrorType.SSL_ERROR
            }
            error.contains("ERR_CLEARTEXT_NOT_PERMITTED") -> ErrorType.SSL_ERROR
            error.contains("ERR_SSL_BAD_RECORD_MAC_ALERT") -> ErrorType.CERTIFICATE_ERROR
            error.contains("解析") || error.contains("parse") || error.contains("format") -> ErrorType.PARSING_ERROR
            error.contains("页面") || error.contains("load") || error.contains("404") -> ErrorType.PAGE_LOAD_ERROR
            error.contains("检测") || error.contains("blocked") || error.contains("forbidden") -> ErrorType.DETECTION_ERROR
            else -> ErrorType.UNKNOWN_ERROR
        }
        
        val retryable = when (errorType) {
            ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR, ErrorType.PAGE_LOAD_ERROR -> true
            ErrorType.DETECTION_ERROR -> true // 可以尝试更换策略重试
            ErrorType.SSL_ERROR, ErrorType.CERTIFICATE_ERROR -> false // SSL错误通常需要配置修复，不适合重试
            ErrorType.PARSING_ERROR -> false // 解析错误通常不需要重试
            ErrorType.UNKNOWN_ERROR -> true
        }

        val maxRetries = when (errorType) {
            ErrorType.DETECTION_ERROR -> 5 // 检测错误多重试几次
            ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR -> 3
            ErrorType.PAGE_LOAD_ERROR -> 2
            ErrorType.SSL_ERROR, ErrorType.CERTIFICATE_ERROR -> 0 // SSL错误不重试
            else -> 1
        }
        
        return ErrorInfo(
            type = errorType,
            message = error,
            cause = cause,
            retryable = retryable,
            maxRetries = maxRetries
        )
    }
    
    /**
     * 获取用户友好的错误消息
     */
    fun getUserFriendlyMessage(errorInfo: ErrorInfo): String {
        return when (errorInfo.type) {
            ErrorType.NETWORK_ERROR -> "网络连接异常，请检查网络设置"
            ErrorType.PARSING_ERROR -> "数据格式异常，可能是页面结构发生变化"
            ErrorType.PAGE_LOAD_ERROR -> "页面加载失败，请稍后重试"
            ErrorType.DETECTION_ERROR -> "访问受限，正在尝试其他方式"
            ErrorType.TIMEOUT_ERROR -> "请求超时，请检查网络连接"
            ErrorType.SSL_ERROR -> "SSL连接失败，请检查网络安全设置"
            ErrorType.CERTIFICATE_ERROR -> "证书验证失败，可能是网络环境限制"
            ErrorType.UNKNOWN_ERROR -> "未知错误：${errorInfo.message}"
        }
    }
    
    /**
     * 计算重试延迟（指数退避 + 随机抖动）
     */
    fun calculateRetryDelay(retryCount: Int): Long {
        val baseDelay = 1000L // 基础延迟1秒
        val exponentialDelay = baseDelay * (1 shl retryCount) // 指数退避
        val jitter = Random.nextLong(0, 1000) // 随机抖动0-1秒
        return exponentialDelay + jitter
    }
    
    /**
     * 执行带重试的操作
     */
    suspend fun <T> executeWithRetry(
        operation: suspend () -> T,
        errorAnalyzer: (Exception) -> ErrorInfo = { analyzeError(it.message ?: "Unknown error", it) },
        onRetry: (ErrorInfo) -> Unit = {},
        onFinalError: (ErrorInfo) -> Unit = {}
    ): Result<T> {
        var lastError: ErrorInfo? = null
        
        repeat(3) { attempt ->
            try {
                return Result.success(operation())
            } catch (e: Exception) {
                val errorInfo = errorAnalyzer(e).copy(retryCount = attempt)
                lastError = errorInfo
                
                Log.w(TAG, "Operation failed (attempt ${attempt + 1}): ${errorInfo.message}", e)
                
                if (attempt < 2 && errorInfo.retryable) {
                    val delay = calculateRetryDelay(attempt)
                    Log.i(TAG, "Retrying in ${delay}ms...")
                    onRetry(errorInfo)
                    delay(delay)
                } else {
                    Log.e(TAG, "Operation failed after ${attempt + 1} attempts", e)
                    onFinalError(errorInfo)
                    return@repeat
                }
            }
        }
        
        return Result.failure(Exception(lastError?.message ?: "Operation failed"))
    }
    
    /**
     * 记录错误统计
     */
    private val errorStats = mutableMapOf<ErrorType, Int>()
    
    fun recordError(errorType: ErrorType) {
        errorStats[errorType] = errorStats.getOrDefault(errorType, 0) + 1
        Log.d(TAG, "Error stats: $errorStats")
    }
    
    fun getErrorStats(): Map<ErrorType, Int> = errorStats.toMap()
    
    fun clearErrorStats() {
        errorStats.clear()
    }
    
    /**
     * 检查是否需要切换策略
     */
    fun shouldSwitchStrategy(errorType: ErrorType): Boolean {
        val count = errorStats.getOrDefault(errorType, 0)
        return when (errorType) {
            ErrorType.DETECTION_ERROR -> count >= 3 // 连续3次检测错误就切换策略
            ErrorType.NETWORK_ERROR -> count >= 5   // 连续5次网络错误
            else -> false
        }
    }
}
